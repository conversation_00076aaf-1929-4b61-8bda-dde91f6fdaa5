name: zupid
description: Da<PERSON> App

# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

version: 0.0.23+23


environment:
  sdk: ">=2.19.0-255.2.beta <3.0.0"

dependencies:
  amplitude_flutter: ^3.13.0
  animations: ^2.0.11
  archive: ^4.0.7
  auto_size_text: ^3.0.0
  blur: ^3.1.0
  cached_network_image: ^3.2.3
  camera: ^0.10.5
  device_info_plus: ^9.1.2
  dio: ^5.6.0
  easy_localization: ^3.0.7
  eventsource: ^0.4.0
  firebase_core: ^2.25.5
  firebase_messaging: ^14.8.0
  flutter:
    sdk: flutter
  flutter_cache_manager: ^3.3.1
  flutter_cashfree_pg_sdk: ^2.0.18+21
  flutter_client_sse: ^1.0.0
  flutter_launcher_icons: ^0.13.1
  flutter_native_image: ^0.0.6+1
  flutter_native_splash: ^2.2.19
  flutter_secure_storage: ^8.0.0
  flutter_spinkit: ^5.2.0
  flutter_staggered_grid_view: ^0.6.2
  flutter_typeahead: ^4.8.0
  fluttertoast: ^8.2.1
  font_awesome_flutter: ^10.4.0
  geolocator: ^9.0.0
  http: any
  image_cropper: ^4.0.1
  image_picker: ^0.8.7+4
  internet_connection_checker: ^1.0.0+1
  intl: ^0.18.1
  package_info_plus: ^4.0.2
  path_provider: ^2.1.4
  payu_checkoutpro_flutter: ^1.1.1
  payu_upi_flutter: ^0.0.1
  provider: ^6.0.5
  pull_to_refresh: ^2.0.0
  rename: ^2.1.1
  sentry_dart_plugin: ^1.2.0
  sentry_flutter: ^7.5.2
  shimmer_animation: ^2.1.0
  sizer: ^2.0.15
  toggle_switch: ^2.1.0
  upgrader: ^10.3.0
  url_launcher: ^6.1.10
  webview_flutter: ^4.2.0
  wheel_picker: ^0.1.1

dev_dependencies:
  flutter_lints: ^2.0.0
  flutter_test:
    sdk: flutter

flutter:
  uses-material-design: true
  assets:
    - assets/images/
    - assets/images/swipe-images/
    - assets/translations/

flutter_launcher_icons:
  android: "launcher_icon"
  ios: false
  image_path: "assets/images/launcher_icon.png"
# sentry:
#   upload_debug_symbols: true
#   upload_source_maps: false
#   upload_sources: false
#   # upload_source_maps: true
#   # include_native_sources: true
#   project: "zupid"
#   org: "chanqo"
#   auth_token: "7e1c935099044d0caf7f9f680a6109ada97e16d5569c45d3868a090d8198bacb"
