import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:zupid/translations/locale_keys.g.dart';
import 'package:sizer/sizer.dart';
import 'package:zupid/common_things/my_colors.dart';

class AboutAppScreen extends StatelessWidget {
  //
  static const routeName = '/about-app-screen';
  //
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        title: Text(LocaleKeys.AboutAppScreen_About_zupid.tr()),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.all(5.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              SizedBox(height: 3.h),
              Text(
                LocaleKeys.AboutAppScreen_Welcome_to_zupid.tr(),
                style: TextStyle(
                    fontSize: 20.sp,
                    fontWeight: FontWeight.bold,
                    color: mainColor),
              ),
              SizedBox(height: 3.h),
              Text(
                LocaleKeys.AboutAppScreen_text_1.tr(),
                style: TextStyle(fontSize: 14.sp),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: .5.h),
              Text(
                LocaleKeys.AboutAppScreen_text_2.tr(),
                style: TextStyle(fontSize: 14.sp),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 5.h),
              Text(
                LocaleKeys.AboutAppScreen_Key_Features.tr(),
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: 2.h),
              ListTile(
                leading: Icon(Icons.check),
                title: Text(
                  LocaleKeys.AboutAppScreen_f_1.tr(),
                  style: TextStyle(
                      fontSize: 11.sp,
                      color: Colors.grey,
                      fontWeight: FontWeight.bold),
                ),
              ),
              ListTile(
                leading: Icon(Icons.check),
                title: Text(
                  LocaleKeys.AboutAppScreen_f_2.tr(),
                  style: TextStyle(
                      fontSize: 11.sp,
                      color: Colors.grey,
                      fontWeight: FontWeight.bold),
                ),
              ),
              ListTile(
                leading: Icon(Icons.check),
                title: Text(
                  LocaleKeys.AboutAppScreen_f_3.tr(),
                  style: TextStyle(
                      fontSize: 11.sp,
                      color: Colors.grey,
                      fontWeight: FontWeight.bold),
                ),
              ),
              ListTile(
                leading: Icon(Icons.check),
                title: Text(
                  LocaleKeys.AboutAppScreen_f_4.tr(),
                  style: TextStyle(
                      fontSize: 11.sp,
                      color: Colors.grey,
                      fontWeight: FontWeight.bold),
                ),
              ),
              ListTile(
                leading: const Icon(Icons.check),
                title: Text(
                  LocaleKeys.AboutAppScreen_f_5.tr(),
                  style: TextStyle(
                      fontSize: 11.sp,
                      color: Colors.grey,
                      fontWeight: FontWeight.bold),
                ),
              ),
              SizedBox(height: 5.h),
              Text(
                LocaleKeys.AboutAppScreen_Premium_Features.tr(),
                style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                    color: mainColor),
              ),
              SizedBox(height: 2.h),
              ListTile(
                leading: Icon(Icons.check),
                title: Text(
                  LocaleKeys.PremiumFeatures_1_title.tr(),
                  style: TextStyle(
                      fontSize: 11.sp,
                      color: Colors.grey,
                      fontWeight: FontWeight.bold),
                ),
              ),
              ListTile(
                leading: Icon(Icons.check),
                title: Text(
                  LocaleKeys.PremiumFeatures_2_title.tr(),
                  style: TextStyle(
                      fontSize: 11.sp,
                      color: Colors.grey,
                      fontWeight: FontWeight.bold),
                ),
              ),
              ListTile(
                leading: Icon(Icons.check),
                title: Text(
                  LocaleKeys.PremiumFeatures_3_title.tr(),
                  style: TextStyle(
                      fontSize: 11.sp,
                      color: Colors.grey,
                      fontWeight: FontWeight.bold),
                ),
              ),
              ListTile(
                leading: Icon(Icons.check),
                title: Text(
                  LocaleKeys.PremiumFeatures_4_title.tr(),
                  style: TextStyle(
                      fontSize: 11.sp,
                      color: Colors.grey,
                      fontWeight: FontWeight.bold),
                ),
              ),
              ListTile(
                leading: Icon(Icons.check),
                title: Text(
                  LocaleKeys.PremiumFeatures_5_title.tr(),
                  style: TextStyle(
                      fontSize: 11.sp,
                      color: Colors.grey,
                      fontWeight: FontWeight.bold),
                ),
              ),
              SizedBox(height: 5.h),
              Text(
                LocaleKeys.AboutAppScreen_test_3.tr(),
                style: TextStyle(
                    fontSize: 15.sp,
                    color: mainColor,
                    fontWeight: FontWeight.w600),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 5.h),
            ],
          ),
        ),
      ),
    );
  }
}
