import 'dart:math';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_cashfree_pg_sdk/api/cferrorresponse/cferrorresponse.dart';
import 'package:flutter_cashfree_pg_sdk/api/cfpayment/cfdropcheckoutpayment.dart';
import 'package:flutter_cashfree_pg_sdk/api/cfpayment/cfupi.dart';
import 'package:flutter_cashfree_pg_sdk/api/cfpayment/cfupipayment.dart';
import 'package:flutter_cashfree_pg_sdk/api/cfpayment/cfwebcheckoutpayment.dart';
import 'package:flutter_cashfree_pg_sdk/api/cfpaymentcomponents/cfpaymentcomponent.dart';
import 'package:flutter_cashfree_pg_sdk/api/cfpaymentgateway/cfpaymentgatewayservice.dart';

import 'package:flutter_cashfree_pg_sdk/api/cfsession/cfsession.dart';
import 'package:flutter_cashfree_pg_sdk/api/cftheme/cftheme.dart';
import 'package:flutter_cashfree_pg_sdk/utils/cfenums.dart';
import 'package:flutter_cashfree_pg_sdk/utils/cfexceptions.dart';
import 'package:zupid/common_things/my_fucntions.dart';
import 'package:zupid/screens/startup_screen.dart';
import 'package:zupid/translations/locale_keys.g.dart';
import 'package:zupid/widgets/loading_widget.dart';
import 'package:payu_checkoutpro_flutter/payu_checkoutpro_flutter.dart';
import 'package:provider/provider.dart';
import 'package:sizer/sizer.dart';
import 'package:zupid/models/myUserInfo.dart';
import 'package:zupid/screens/transaction_status_screen.dart';

import '../common_things/analytics.dart';
import '../common_things/my_colors.dart';
import '../common_things/my_payu.dart';
import '../providers/auth_provider.dart';
import '../providers/database_provider.dart';

class BuyPremiumCashfreeScreen extends StatefulWidget {
  static const routeName = '/buy-premium-cashfree-screen';

  @override
  _BuyPremiumCashfreeScreenState createState() =>
      _BuyPremiumCashfreeScreenState();
}

class _BuyPremiumCashfreeScreenState extends State<BuyPremiumCashfreeScreen> {
  //
  bool _isAnalytics = true;
  late final MyAnalytics myAnalytics;
  //
  bool _isLoading = false;
  Map<String, dynamic> orderCreated = {};
  double amount = 0;
  String orderId = '';
  String paymentSessionId = '';
  CFEnvironment environment = CFEnvironment.PRODUCTION;

  // String _userName = '';
  int totalMonths = 0;
  int monthlyPrice = 0;
  //
  var cfPaymentGatewayService = CFPaymentGatewayService();
  //
  int _selectedDuration = 2;
  List<dynamic> _pricingOptions = [];

  ///
  void _showPaymentDialog(MyUserInfo _myUserInfo, int phoneNumber) {
    // print('show payment dialog');
    final option = _pricingOptions[_selectedDuration];
    totalMonths = option['months'];
    monthlyPrice = option['price'];
    //
    int subTotal = option['price'] * totalMonths;
    int gst = (subTotal * 0.18) ~/ 1;
    int totalAmount = (subTotal + gst) ~/ 1;
    //
    amount = double.parse(totalAmount.toString());
    //

    showDialog(
      context: context,
      builder: (BuildContext context) => Dialog(
        child: Padding(
          padding: EdgeInsets.all(3.h),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                LocaleKeys.BuyPremiumScreen_Payment_Details.tr(),
                style: TextStyle(
                  color: mainColor,
                  fontSize: 14.sp,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: 4.h),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '${LocaleKeys.BuyPremiumScreen_Subscription_Fee.tr()} (${option['duration']}) x $totalMonths',
                    style: TextStyle(
                      color: mainColor,
                    ),
                  ),
                  Text(
                    '\u{20B9} $subTotal',
                    style: TextStyle(
                      color: mainColor,
                    ),
                  ),
                ],
              ),
              SizedBox(height: 2.h),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'GST (18%)',
                    style: TextStyle(
                      color: mainColor,
                    ),
                  ),
                  Text(
                    '\u{20B9} $gst',
                    style: TextStyle(
                      color: mainColor,
                    ),
                  ),
                ],
              ),
              SizedBox(height: 4.h),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    LocaleKeys.BuyPremiumScreen_Total_Amount.tr(),
                    style: const TextStyle(
                        fontWeight: FontWeight.bold, color: mainColor),
                  ),
                  Text(
                    '\u{20B9} ${totalAmount.toStringAsFixed(2)}',
                    style: const TextStyle(
                      color: mainColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              SizedBox(height: 4.h),
              ElevatedButton(
                style: ButtonStyle(
                  backgroundColor: MaterialStateProperty.all(mainColor),
                ),
                onPressed: () async {
                  Navigator.pop(context);
                  setState(() {
                    _isLoading = true;
                  });
                  orderCreated = await Provider.of<DatabaseProvider>(context,
                          listen: false)
                      .cashfreeCreateOrder(amount, totalMonths, monthlyPrice);
                  setState(() {
                    _isLoading = false;
                  });
                  if (orderCreated.isNotEmpty) {
                    orderId = orderCreated['order_id']!;
                    paymentSessionId = orderCreated['payment_session_id']!;
                    print('opening checkput screen');
                    webCheckout();
                    print('opening checkput screen: DONE');
                    Navigator.of(context).pop();
                  } else {
                    callToast('Some Error Occured');
                  }
                },
                child: Text(
                  LocaleKeys.BuyPremiumScreen_Pay_Now.tr(),
                  style: const TextStyle(color: Colors.white),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  webCheckout() async {
    try {
      var session = createSession();
      var theme = CFThemeBuilder()
          .setNavigationBarBackgroundColorColor("#ffffff")
          .setNavigationBarTextColor("#ffffff")
          .build();
      var cfWebCheckout = CFWebCheckoutPaymentBuilder()
          .setSession(session!)
          .setTheme(theme)
          .build();
      cfPaymentGatewayService.doPayment(cfWebCheckout);
    } on CFException catch (e) {
      // print(e.message);
    }
  }

  void verifyPayment(String orderId) {
    print("Verify Payment");
    Navigator.of(context).pop();
  }

  void onError(CFErrorResponse errorResponse, String orderId) {
    // print(errorResponse.getMessage());
    print("Error while making payment");
  }

  void receivedEvent(String event_name, Map<dynamic, dynamic> meta_data) {
    // print("event: $event_name");
    // print("meta data: $meta_data");
  }

  @override
  void initState() {
    super.initState();
    cfPaymentGatewayService.setCallback(verifyPayment, onError);
  }

  CFSession? createSession() {
    try {
      var session = CFSessionBuilder()
          .setEnvironment(environment)
          .setOrderId(orderId)
          .setPaymentSessionId(paymentSessionId)
          .build();
      return session;
    } on CFException catch (e) {
      // print(e.message);
    }
    return null;
  }

  @override
  Widget build(BuildContext context) {
    //analytics
    if (_isAnalytics) {
      myAnalytics = Provider.of<MyAnalytics>(context, listen: false);
      myAnalytics.setLogEvent('BuyPremiumScreen', {});
      _isAnalytics = false;
    }
    //
    MyUserInfo _myUserInfo =
        Provider.of<DatabaseProvider>(context, listen: false).getMyUserInfo;
    // _userName = _myUserInfo.firstName;
    // _userId = _myUserInfo.userId;
    // _phoneNumber =
    //     Provider.of<DatabaseProvider>(context, listen: false).getMyPhoneNumber;
    //
    _pricingOptions =
        Provider.of<AuthProvider>(context, listen: false).getMembershipPlans;
    return _isLoading
        ? StartupScreen()
        : Scaffold(
            appBar: AppBar(
              actions: [
                Padding(
                  padding: EdgeInsets.all(2.w),
                  child: ElevatedButton(
                    style: const ButtonStyle(
                      backgroundColor: MaterialStatePropertyAll(
                        thirdColor,
                      ),
                    ),
                    onPressed: () {
                      //
                      print('show payment dialog: run');

                      int phoneNumber =
                          Provider.of<DatabaseProvider>(context, listen: false)
                              .getMyPhoneNumber;
                      _showPaymentDialog(_myUserInfo, phoneNumber);
                      print('show payment dialog: done');
                    },
                    child: Text(
                      LocaleKeys.BuyPremiumScreen_Buy_Now.tr(),
                      style: const TextStyle(color: mainColor),
                    ),
                  ),
                )
              ],
            ),
            body: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(height: 3.h),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 5.w, vertical: 1.h),
                  child: Text(
                    LocaleKeys.BuyPremiumScreen_Upgrade_to_zupid_Premium.tr(),
                    style: TextStyle(
                        fontSize: 17.sp,
                        fontWeight: FontWeight.bold,
                        color: mainColor),
                  ),
                ),
                SizedBox(height: 2.h),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 5.w, vertical: 1.h),
                  child: Text(
                    LocaleKeys.BuyPremiumScreen_Choose_a_Subscription.tr(),
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                SizedBox(height: 2.h),
                Expanded(
                  child: ListView.builder(
                    itemCount: _pricingOptions.length,
                    itemBuilder: (context, index) {
                      final isSelected = _selectedDuration == index;
                      final option = _pricingOptions[index];
                      final savings = option['savings'];
                      return GestureDetector(
                        onTap: () {
                          setState(() {
                            _selectedDuration = index;
                          });
                        },
                        child: Container(
                          margin: EdgeInsets.symmetric(
                              horizontal: 5.w, vertical: 1.h),
                          padding: EdgeInsets.symmetric(
                              horizontal: 5.w, vertical: 2.h),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(2.5.h),
                            color: isSelected
                                ? mainContrastColor
                                : Colors.grey[300],
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                option['duration'],
                                style: TextStyle(
                                  color:
                                      isSelected ? Colors.white : Colors.black,
                                  fontSize: 16.sp,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              SizedBox(height: 1.h),
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    '\u{20B9} ${option['price']}/${LocaleKeys.BuyPremiumScreen_month.tr()}',
                                    style: TextStyle(
                                      color: isSelected
                                          ? Colors.white
                                          : Colors.black,
                                    ),
                                  ),
                                  if (savings > 0)
                                    Text(
                                      '${LocaleKeys.BuyPremiumScreen_Save.tr()} $savings%',
                                      style: TextStyle(
                                        color: isSelected
                                            ? Colors.white
                                            : Colors.black54,
                                        fontSize: 11.sp,
                                        fontWeight: FontWeight.w700,
                                      ),
                                    ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          );
  }
}

Widget _buildFeatureItem(String title, String description) {
  return Container(
    width: 100.w,
    child: Padding(
      padding: EdgeInsets.symmetric(vertical: 1.5.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            Icons.check_circle_outline,
            color: mainColor,
            size: 2.5.h,
          ),
          SizedBox(width: 2.h),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    color: Colors.black,
                    fontSize: 12.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 0.5.h),
                Text(
                  description,
                  maxLines: 3,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(
                    color: Colors.black54,
                    fontSize: 10.sp,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    ),
  );
}
