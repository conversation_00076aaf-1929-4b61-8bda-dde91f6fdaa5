import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:zupid/common_things/my_colors.dart';
import 'package:zupid/common_things/my_fucntions.dart';
import 'package:zupid/providers/database_provider.dart';
import 'package:zupid/screens/movie_search_screen.dart';
import 'package:zupid/widgets/movie_card_widget.dart';
import 'package:provider/provider.dart';
import 'package:sizer/sizer.dart';

class EditMovieScreen extends StatefulWidget {
  static const routeName = '/edit-movie-screen';
  const EditMovieScreen({super.key});

  @override
  State<EditMovieScreen> createState() => _EditMovieScreenState();
}

class _EditMovieScreenState extends State<EditMovieScreen> {
  @override
  Widget build(BuildContext context) {
    final myUserInfo =
        Provider.of<DatabaseProvider>(context, listen: true).getMyUserInfo;

    return Scaffold(
      backgroundColor: mainContrastColor.withOpacity(0.2),
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        title: Center(child: const Text('Add Movies & TV Shows')),
      ),
      body: Padding(
        padding: EdgeInsets.all(2.w),
        child: Column(
          children: [
            Text('* Minimum of 3 movies is required'),
            SizedBox(
              height: 2.h,
            ),
            Expanded(
              child: GridView.count(
                shrinkWrap: true,
                // physics: NeverScrollableScrollPhysics(),
                crossAxisCount: 3,
                childAspectRatio: .7,
                children: [
                  // First element for "Add +" button
                  AddMovieCardWidget(),
                  // Map existing movies with "X" button for removal
                  ...myUserInfo.allInterests.movies.map((movie) {
                    return Stack(
                      children: [
                        MovieCardWidget(
                          imageUrl: movie.imagePath,
                          movieName: movie.title,
                          movieYear: movie.year,
                        ),
                        // Positioned X button on top right of each movie card
                        Positioned(
                          top: 5,
                          right: 5,
                          child: GestureDetector(
                            onTap: () {
                              setState(() {
                                if (myUserInfo.allInterests.movies.length <=
                                    3) {
                                  callSnackfold(context,
                                      'Minimum of 3 movies is required');
                                  return;
                                }
                                callSnackfold(context,
                                    'Deleting \'${movie.title}\' from your interests!');
                                Provider.of<DatabaseProvider>(context,
                                        listen: false)
                                    .UserDeleteAMovie(movie.imdbId);
                                myUserInfo.allInterests.movies.remove(movie);
                              });
                            },
                            child: Container(
                              decoration: const BoxDecoration(
                                color: thirdColor,
                                shape: BoxShape.circle,
                              ),
                              child: const Icon(
                                Icons.close,
                                color: mainColor,
                                size: 20,
                              ),
                            ),
                          ),
                        ),
                      ],
                    );
                  }).toList(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class AddMovieCardWidget extends StatelessWidget {
  const AddMovieCardWidget({
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(2.w),
      child: GestureDetector(
        onTap: () {
          // Navigate to the movie search screen when tapped
          Navigator.of(context).pushNamed(MovieSearchScreen.routeName);
        },
        child: Container(
          height: 40.w,
          decoration: BoxDecoration(
            color: mainColor.withOpacity(0.8),
            borderRadius: BorderRadius.circular(15),
            boxShadow: const [
              BoxShadow(
                color: mainContrastColor,
                blurRadius: 2,
                offset: Offset(1, 2),
              ),
            ],
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Icon(
                Icons.add,
                size: 15.w, // Make the icon size similar to the image height
                color: thirdColor,
              ),
              SizedBox(height: 3.w),
              Text(
                'Add',
                style: TextStyle(
                  fontWeight: FontWeight.w700,
                  fontSize: 12.sp,
                  color: thirdColor,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
