import 'dart:async';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:zupid/common_things/images.dart';
import 'package:zupid/common_things/my_fucntions.dart';
import 'package:zupid/translations/locale_keys.g.dart';
import 'package:provider/provider.dart';
import 'package:sizer/sizer.dart';
import 'package:zupid/common_things/my_colors.dart';
import 'package:zupid/widgets/loading_widget.dart';

import '../common_things/analytics.dart';
import '../providers/auth_provider.dart';

class OtpScreen extends StatefulWidget {
  static const routeName = '/otp_screen';
  @override
  State<OtpScreen> createState() => _OtpScreenState();
}

class _OtpScreenState extends State<OtpScreen> {
  /////
  bool _isAnalytics = true;
  late final MyAnalytics myAnalytics;
  //////
  final _otpKey = GlobalKey<FormState>();
  Timer? _timer;
  final int countdown = 60;
  int _start = 60;
  bool isSend = false;
  String? _phoneNumber;
  String? _otp;
  bool? sendResult;
  bool _showResendButton = false;
  bool _isLoading = false;

  void startTimer() {
    const oneSec = Duration(seconds: 1);
    _timer = Timer.periodic(
      oneSec,
      (Timer timer) {
        if (_start == 0) {
          setState(() {
            timer.cancel();
            _start = countdown;
            _showResendButton = true;
          });
        } else {
          setState(() {
            _start--;
          });
        }
      },
    );
  }

  @override
  void dispose() {
    _timer!.cancel();
    super.dispose();
  }

  @override
  void initState() {
    startTimer();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    //////////
    if (_isAnalytics) {
      myAnalytics = Provider.of<MyAnalytics>(context, listen: false);
      myAnalytics.setLogEvent('OtpScreen', {});
      _isAnalytics = false;
    }
    _phoneNumber = Provider.of<AuthProvider>(context, listen: false)
        .getPhoneNumber
        .toString();
    ////////////
    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        title: Text(
          LocaleKeys.otpscreen_Verify_Phone_Number.tr(),
          // style: const TextStyle(
          //   color: mainColor,
          //   fontWeight: FontWeight.bold,
          // ),
        ),
      ),
      body: _isLoading
          ? LoadingWidget()
          : Padding(
              padding: EdgeInsets.fromLTRB(10.w, 0, 10.w, 0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  // Center(
                  //   child: Image.asset(
                  //     height: 10.h,
                  //     MyImages.kOtp,
                  //   ),
                  // ),
                  // if (_phoneNumber != null)
                  Align(
                    alignment: Alignment.center,
                    child: Padding(
                      padding: EdgeInsets.only(bottom: 2.h),
                      child: Text(
                        "We’ve sent an OTP on WhatsApp to $_phoneNumber",
                        style: TextStyle(
                          fontSize: 11.sp,
                          color: mainContrastColor,
                          fontWeight: FontWeight.w600,
                          letterSpacing: 0.3,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                  SizedBox(
                    height: 2.h,
                  ),
                  Form(
                    key: _otpKey,
                    child: TextFormField(
                      style: const TextStyle(
                          fontWeight: FontWeight.bold, color: thirdColor),
                      decoration: InputDecoration(
                        filled: true,
                        fillColor:
                            mainColor, // Light background for modern look
                        floatingLabelBehavior: FloatingLabelBehavior.never,
                        contentPadding: EdgeInsets.symmetric(
                            vertical: 2.h, horizontal: 3.w),
                        hintStyle:
                            TextStyle(color: thirdColor, fontSize: 12.sp),
                        border: OutlineInputBorder(
                          borderRadius:
                              BorderRadius.circular(12.sp), // Rounded corners
                          borderSide:
                              BorderSide.none, // No border, relying on shadow
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12.sp),
                          borderSide: BorderSide(
                            color:
                                thirdColor, // Your main color for focus border
                            width: 2.sp,
                          ),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12.sp),
                          borderSide: BorderSide(
                            color:
                                thirdColor, // Your main color for focus border
                            width: 2.sp,
                          ),
                        ),
                        label: Text(
                          LocaleKeys.otpscreen_Please_enter_OTP.tr(),
                          style: TextStyle(
                            color: thirdColor,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      keyboardType: TextInputType.number,
                      inputFormatters: <TextInputFormatter>[
                        FilteringTextInputFormatter.digitsOnly,
                        LengthLimitingTextInputFormatter(6),
                      ],
                      enabled: true,
                      validator: (value) {
                        if (value!.length != 6) {
                          return LocaleKeys.otpscreen_Enter_6_digit_OTP.tr();
                        }
                      },
                      onSaved: (value) {
                        _otp = value;
                      },
                    ),
                  ),
                  SizedBox(height: 1.h),
                  if (_start != countdown)
                    Padding(
                      padding: EdgeInsets.fromLTRB(0, 0, 0, 1.h),
                      child: Text(
                        '${LocaleKeys.otpscreen_retry_in.tr()} $_start ${LocaleKeys.otpscreen_seconds.tr()}',
                        style: TextStyle(
                          fontSize: 10.sp,
                          fontWeight: FontWeight.w600,
                          color: thirdColor,
                        ),
                      ),
                    ),
                  if (_showResendButton)
                    InkWell(
                      child: Text(
                        LocaleKeys.otpscreen_Resend_OTP.tr(),
                        style: TextStyle(
                          fontSize: 10.sp,
                          fontWeight: FontWeight.bold,
                          color: thirdColor,
                        ),
                      ),
                      onTap: () async {
                        setState(() {
                          _showResendButton = false;
                        });
                        startTimer();
                        bool result = await Provider.of<AuthProvider>(context,
                                listen: false)
                            .resendlogin();
                      },
                    ),
                  SizedBox(height: 3.h),
                  Container(
                    width: double.infinity,
                    child: ElevatedButton(
                      style: ButtonStyle(
                        backgroundColor: MaterialStateProperty.all(thirdColor),
                        foregroundColor: MaterialStateProperty.all(
                            Colors.white), // Text color
                      ),
                      onPressed: () async {
                        if (!_otpKey.currentState!.validate()) {
                          return;
                        }
                        _otpKey.currentState!.save();
                        {
                          setState(() {
                            _isLoading = true;
                          });
                          // print(_otp);
                          bool _isVerified = await Provider.of<AuthProvider>(
                                  context,
                                  listen: false)
                              .verifyOtp(int.parse(_otp!));
                          if (!_isVerified) {
                            callToast('Wrong OTP');
                          }
                          setState(() {
                            _isLoading = false;
                          });

                          // if (!_isVerified) {
                          //   myAnalytics.setLogEvent(
                          //       'VerifyOTP', {'status': 'wrongOTP'});
                          //   ScaffoldMessenger.of(context).showSnackBar(
                          //     const SnackBar(content: Text('Wrong OTP!')),
                          //   );
                          //   startTimer();
                          // }
                          // if (_isVerified) {
                          //   myAnalytics.setLogEvent(
                          //       'VerifyOTP', {'status': 'rightOTP'});
                          // }
                        }
                      },
                      child: Text(
                        LocaleKeys.otpscreen_Verify_OTP.tr(),
                        style: TextStyle(
                            color: mainColor, fontWeight: FontWeight.bold),
                      ),
                    ),
                  ),
                  SizedBox(height: 5.h),
                ],
              ),
            ),
    );
  }
}
