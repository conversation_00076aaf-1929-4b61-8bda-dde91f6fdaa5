import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:zupid/common_things/analytics.dart';
import 'package:zupid/common_things/images.dart';
import 'package:zupid/common_things/my_colors.dart';
import 'package:zupid/common_things/my_fucntions.dart';
import 'package:zupid/providers/auth_provider.dart';
import 'package:zupid/screens/buy_premium_screen.dart';
import 'package:zupid/translations/locale_keys.g.dart';
import 'package:provider/provider.dart';
import 'package:sizer/sizer.dart';
import 'package:zupid/providers/database_provider.dart';
import 'package:url_launcher/url_launcher.dart';

class ReferralScreen extends StatefulWidget {
  static const routeName = '/referral-screen';

  @override
  State<ReferralScreen> createState() => _ReferralScreenState();
}

class _ReferralScreenState extends State<ReferralScreen> {
  /////
  bool _isAnalytics = true;
  late final MyAnalytics myAnalytics;
  //////
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    if (_isAnalytics) {
      myAnalytics = Provider.of<MyAnalytics>(context, listen: false);
      myAnalytics.setLogEvent('ReferralScreen', {});
      _isAnalytics = false;
    }
    //
    final userInfo = Provider.of<DatabaseProvider>(context).getMyUserInfo;
    final referral = Provider.of<DatabaseProvider>(context).getMyReferral;
    final referralCode = referral.promoCode;
    final totalReferrals = referral.totalReferrals;
    final totalCoins = referral.totalCoins;
    //
    // Define the number of coins required for premium
    int requiredCoins = Provider.of<AuthProvider>(context, listen: false)
        .getReferralBuyPremiumCoins;
    int coinsPerReferral =
        Provider.of<AuthProvider>(context, listen: false).getCoinsPerReferral;

    final int coinsLeft = requiredCoins - totalCoins;
    final double progress = totalCoins / requiredCoins;

    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        title: Text(
          'Earn Premium by Referring',
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.all(5.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: 1.h),
              Container(
                padding: EdgeInsets.all(5.w),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12.0),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 10.0,
                      offset: Offset(0, 5),
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    Row(
                      children: [
                        Image.asset(
                          MyImages.kCoin,
                          width: 10.w,
                          height: 10.w,
                        ),
                        SizedBox(width: 5.w),
                        Expanded(
                          child: Text(
                            'Total coins earned: $totalCoins',
                            style: TextStyle(
                              fontSize: 13.sp,
                              color: mainContrastColor,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 3.h),
                    LinearProgressIndicator(
                      value: progress.clamp(0, 1),
                      backgroundColor: Colors.grey[300],
                      valueColor:
                          AlwaysStoppedAnimation<Color>(mainContrastColor),
                      color: thirdColor,
                    ),
                    SizedBox(height: 0.5.h),
                    Text(
                      coinsLeft > 0
                          ? '$coinsLeft coins left to unlock Premium'
                          : 'You have enough coins for Premium!',
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: coinsLeft > 0 ? Colors.grey : mainColor,
                      ),
                    ),
                    SizedBox(height: 2.h),
                    // Progress bar and text for coins needed
                    Padding(
                      padding:
                          EdgeInsets.symmetric(vertical: 0, horizontal: 5.w),
                      child: Center(
                        child: ElevatedButton(
                          style: ButtonStyle(
                              padding: MaterialStateProperty.all(
                                EdgeInsets.symmetric(
                                    vertical: 1.5.h, horizontal: 10.w),
                              ),
                              shape: MaterialStateProperty.all<
                                  RoundedRectangleBorder>(
                                RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(3.h),
                                ),
                              ),
                              backgroundColor: MaterialStatePropertyAll(
                                  coinsLeft > 0 ? Colors.grey : thirdColor)),
                          onPressed: () {
                            coinsLeft > 0
                                ? null
                                : userInfo.isMembership
                                    ? callToast(
                                        "You already have an active membership!")
                                    : showDialog(
                                        context: context,
                                        builder: (BuildContext context) {
                                          return AlertDialog(
                                            backgroundColor: mainColor,
                                            shape: RoundedRectangleBorder(
                                              borderRadius:
                                                  BorderRadius.circular(20),
                                            ),
                                            title: Text(
                                              'Confirm Purchase',
                                              style: TextStyle(
                                                  fontSize: 18.sp,
                                                  fontWeight: FontWeight.bold,
                                                  color: mainContrastColor),
                                            ),
                                            content: Text(
                                              'Unlock 1 month of premium access for just $requiredCoins coins!\n\nWould you like to proceed with the deduction?',
                                              style: TextStyle(
                                                  fontSize: 13.sp,
                                                  color: mainContrastColor),
                                            ),
                                            actions: <Widget>[
                                              TextButton(
                                                child: Text(
                                                  'Cancel',
                                                  style: TextStyle(
                                                    fontSize: 12.sp,
                                                    color: Colors.grey,
                                                  ),
                                                ),
                                                onPressed: () {
                                                  Navigator.of(context)
                                                      .pop(); // Close dialog
                                                },
                                              ),
                                              ElevatedButton(
                                                style: ButtonStyle(
                                                  backgroundColor:
                                                      MaterialStateProperty.all(
                                                          thirdColor),
                                                  padding:
                                                      MaterialStateProperty.all(
                                                    EdgeInsets.symmetric(
                                                        vertical: 1.h,
                                                        horizontal: 5.w),
                                                  ),
                                                  shape:
                                                      MaterialStateProperty.all<
                                                          RoundedRectangleBorder>(
                                                    RoundedRectangleBorder(
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              12),
                                                    ),
                                                  ),
                                                ),
                                                child: Text(
                                                  'Buy Now',
                                                  style: TextStyle(
                                                    color: mainColor,
                                                    fontSize: 12.5.sp,
                                                  ),
                                                ),
                                                onPressed: () async {
                                                  // Call your server function to complete the purchase
                                                  // For example:
                                                  setState(() {
                                                    _isLoading = true;
                                                  });
                                                  final result = await Provider
                                                          .of<DatabaseProvider>(
                                                              context,
                                                              listen: false)
                                                      .referralBuyPremium();
                                                  setState(() {
                                                    _isLoading = false;
                                                  });
                                                  Navigator.of(context)
                                                      .pop(); // Close the dialog after action
                                                  if (result) {
                                                    callToast(
                                                        'Purchase successful. Enjoy premium features!');
                                                  } else {
                                                    callToast(
                                                        'Purchase failed. Try again!');
                                                  }
                                                },
                                              ),
                                            ],
                                          );
                                        },
                                      );
                          },
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Image.asset(
                                MyImages.kCoin,
                                width: 5.w,
                                height: 5.w,
                              ),
                              SizedBox(width: 2.w),
                              Text(
                                LocaleKeys.PremiumFeatures_Buy_Premium.tr(),
                                style: TextStyle(
                                  color:
                                      coinsLeft > 0 ? Colors.white : mainColor,
                                  fontSize: 12.5.sp,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(height: 4.h),
              Text(
                'How it works:',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: mainContrastColor,
                  fontWeight: FontWeight.w500,
                ),
              ),
              SizedBox(height: 1.h),
              Text(
                '• Share your referral code with friends and family.',
                style: TextStyle(
                  fontSize: 12.sp,
                  color: mainContrastColor,
                ),
              ),
              Text(
                '• When they sign up using your code, both you and your friend will earn $coinsPerReferral coins each.',
                style: TextStyle(
                  fontSize: 12.sp,
                  color: mainContrastColor,
                ),
              ),
              Text(
                '• Coins can only be used to buy premium in the app.',
                style: TextStyle(
                  fontSize: 12.sp,
                  color: mainContrastColor,
                ),
              ),
              Text(
                '• Enjoy 1 month of premium access for just $requiredCoins coins.',
                style: TextStyle(
                  fontSize: 12.sp,
                  color: mainContrastColor,
                ),
              ),
              SizedBox(height: 3.h),
              Container(
                padding: EdgeInsets.all(4.w),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12.0),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 10.0,
                      offset: Offset(0, 5),
                    ),
                  ],
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      'Referral Code: $referralCode',
                      style: TextStyle(
                        fontSize: 13.sp,
                        color: mainColor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(
                height: 5.h,
              ),
              Align(
                alignment: Alignment.center,
                child: ElevatedButton.icon(
                  onPressed: () async {
                    myAnalytics.setLogEvent('ReferralClick', {});
                    String message =
                        "${LocaleKeys.SettingsScreen_share_message_1.tr()}\n\n${LocaleKeys.SettingsScreen_share_message_2.tr()}\nhttps://play.google.com/store/apps/details?id=net.zupid.app\n\n${LocaleKeys.SettingsScreen_share_message_3.tr()} $referralCode";
                    String url = "whatsapp://send?text=$message";
                    try {
                      await launch(url);
                    } catch (error) {
                      callToast(LocaleKeys.SettingsScreen_WhatsApp_not_installed
                          .tr());
                    }
                  },
                  icon: const FaIcon(
                    FontAwesomeIcons.whatsapp,
                    color: Colors.white,
                  ),
                  // iconColor: Colors.green,
                  // leading: const FaIcon(FontAwesomeIcons.whatsapp),
                  label: Text(
                    LocaleKeys.SettingsScreen_Share_with_Friends.tr(),
                    style: const TextStyle(
                        color: Colors.white, fontWeight: FontWeight.w500),
                  ),
                  style: ButtonStyle(
                    backgroundColor: MaterialStateProperty.all(Colors.green),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
