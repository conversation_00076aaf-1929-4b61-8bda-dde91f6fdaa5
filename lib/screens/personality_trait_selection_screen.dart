import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:zupid/common_things/my_colors.dart';
import 'package:zupid/models/myPersonalityTraits.dart';
import 'package:zupid/models/myUserInfo.dart';
import 'package:zupid/providers/database_provider.dart';
import 'package:provider/provider.dart';
import 'package:sizer/sizer.dart';

class PersonalityTraitSelectionScreen extends StatefulWidget {
  static const routeName = '/personality_traits_selection_screen';

  @override
  _PersonalityTraitSelectionScreenState createState() =>
      _PersonalityTraitSelectionScreenState();
}

class _PersonalityTraitSelectionScreenState
    extends State<PersonalityTraitSelectionScreen> {
  List<Map<String, dynamic>> _userPersonalityTraits = [];
  int _currentCategoryIndex = 0;
  List<String> _categories = [];

  @override
  void initState() {
    super.initState();
    _initializePersonalityTraits();
  }

  void _initializePersonalityTraits() {
    final userInfo =
        Provider.of<DatabaseProvider>(context, listen: false).getMyUserInfo;
    final userTraits = userInfo.allInterests.personalityTraitsJson;
    final allTraitIds = Provider.of<DatabaseProvider>(context, listen: false)
        .getAllPersonalityTraitsList;

    final Map<String, List<Map<String, dynamic>>> traitsByCategory = {};
    for (var trait in allTraitIds) {
      final userTrait = userTraits.firstWhere(
        (userTrait) => userTrait.traitId == trait.id,
        orElse: () => PersonalityTraitsJson(traitId: trait.id, value: 0),
      );

      final category = trait.category;
      if (!traitsByCategory.containsKey(category)) {
        traitsByCategory[category] = [];
      }

      traitsByCategory[category]!.add({
        'id': trait.id,
        'category': trait.category,
        'name': trait.name,
        'value': userTrait.value.toDouble(),
        'min': trait.min,
        'max': trait.max,
      });
    }

    _categories = traitsByCategory.keys.where((category) {
      return traitsByCategory[category]!.any((trait) => trait['value'] == 0);
    }).toList();

    if (_categories.isNotEmpty) {
      _userPersonalityTraits =
          traitsByCategory[_categories[_currentCategoryIndex]]!;
    }
  }

  void _goToNextCategory() {
    if (_currentCategoryIndex < _categories.length - 1) {
      setState(() {
        _currentCategoryIndex++;
        _userPersonalityTraits =
            _getTraitsForCategory(_categories[_currentCategoryIndex]);
      });
    } else {
      null;
    }
  }

  List<Map<String, dynamic>> _getTraitsForCategory(String category) {
    return Provider.of<DatabaseProvider>(context, listen: false)
        .getAllPersonalityTraitsList
        .where((trait) => trait.category == category)
        .map((trait) {
      final userTrait = _userPersonalityTraits.firstWhere(
        (userTrait) => userTrait['id'] == trait.id,
        orElse: () => {'id': trait.id, 'value': 0},
      );

      return {
        'id': trait.id,
        'category': trait.category,
        'name': trait.name,
        'value': userTrait['value'],
        'min': trait.min,
        'max': trait.max,
      };
    }).toList();
  }

  bool _areAllTraitsValid() {
    for (var trait in _userPersonalityTraits) {
      if (trait['value'] <= 0) {
        return false;
      }
    }
    return true;
  }

  void _savePersonalityTraits() {
    if (!_areAllTraitsValid()) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Please rate all traits from 1 to 5.'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    final traitsMap = <String, int>{};
    for (var trait in _userPersonalityTraits) {
      traitsMap[trait['id']] = trait['value'].toInt();
    }

    Provider.of<DatabaseProvider>(context, listen: false)
        .updatePersonalityTraitsBatch(traitsMap);
    _goToNextCategory();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: mainColor,
      appBar: AppBar(
        elevation: 0,
        title: Center(
          child: Text(
            _categories[_currentCategoryIndex],
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.w600,
              color: thirdColor,
            ),
            textAlign: TextAlign.center,
          ),
        ),
        backgroundColor: Colors.transparent,
        automaticallyImplyLeading: false,
        systemOverlayStyle: SystemUiOverlayStyle(
          statusBarColor: mainColor,
          statusBarIconBrightness: Brightness.light,
        ),
      ),
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: 5.w, vertical: 2.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              'Rate each trait from 1 to 5 stars',
              style: TextStyle(
                fontSize: 12.sp,
                color: mainContrastColor.withOpacity(0.8),
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 3.h),
            Expanded(
              child: ListView.separated(
                itemCount: _userPersonalityTraits.length,
                separatorBuilder: (context, index) => Divider(
                  color: Colors.grey.withOpacity(0.3),
                  height: 2.h,
                ),
                itemBuilder: (context, index) {
                  final trait = _userPersonalityTraits[index];
                  return Padding(
                    padding: EdgeInsets.symmetric(vertical: 1.h),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Text(
                          trait['name'],
                          style: TextStyle(
                            fontSize: 14.sp,
                            fontWeight: FontWeight.w500,
                            color: mainContrastColor,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        SizedBox(height: 1.h),
                        // Descriptive text above stars
                        Padding(
                          padding: EdgeInsets.symmetric(horizontal: 5.w),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                trait['min'],
                                style: TextStyle(
                                  fontSize: 10.sp,
                                  color:
                                      trait['value'] < 3 && trait['value'] > 0
                                          ? thirdColor
                                          : Colors.grey,
                                  fontWeight:
                                      trait['value'] < 3 && trait['value'] > 0
                                          ? FontWeight.bold
                                          : FontWeight.normal,
                                ),
                              ),
                              Text(
                                trait['max'],
                                style: TextStyle(
                                  fontSize: 10.sp,
                                  color: trait['value'] > 3
                                      ? thirdColor
                                      : Colors.grey,
                                  fontWeight: trait['value'] > 3
                                      ? FontWeight.bold
                                      : FontWeight.normal,
                                ),
                              ),
                            ],
                          ),
                        ),
                        SizedBox(height: 0.5.h),
                        // Star rating row
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: List.generate(5, (starIndex) {
                            return GestureDetector(
                              onTap: () {
                                setState(() {
                                  trait['value'] = starIndex + 1;
                                });
                              },
                              child: Container(
                                margin: EdgeInsets.symmetric(horizontal: 1.w),
                                child: Icon(
                                  starIndex < trait['value']
                                      ? Icons.star
                                      : Icons.star_border,
                                  color: starIndex < trait['value']
                                      ? thirdColor
                                      : Colors.grey.withOpacity(0.7),
                                  size: 24.sp,
                                ),
                              ),
                            );
                          }),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
            if (_areAllTraitsValid())
              Padding(
                padding: EdgeInsets.only(top: 2.h),
                child: SizedBox(
                  width: 60.w,
                  height: 6.h,
                  child: ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: thirdColor,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(2.h),
                      ),
                      elevation: 3,
                      shadowColor: Colors.black.withOpacity(0.2),
                    ),
                    onPressed: _savePersonalityTraits,
                    child: Text(
                      'Next',
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: mainColor,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ),
            SizedBox(height: 2.h),
          ],
        ),
      ),
    );
  }
}
