import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:zupid/common_things/my_fucntions.dart';
import 'package:provider/provider.dart';
import 'package:sizer/sizer.dart';

import 'package:zupid/common_things/my_colors.dart';
import 'package:zupid/providers/auth_provider.dart';
import 'package:zupid/translations/locale_keys.g.dart';

class ChooseLanguageScreen extends StatefulWidget {
  final bool firstTime;
  static const routename = '/choose-language-screen';

  const ChooseLanguageScreen({super.key, required this.firstTime});

  @override
  State<ChooseLanguageScreen> createState() => _ChooseLanguageScreenState();
}

class _ChooseLanguageScreenState extends State<ChooseLanguageScreen> {
  @override
  Widget build(BuildContext context) {
    return widget.firstTime
        ? Scaffold(
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SelectLang(
                    languageLocale: 'en',
                    languageName: 'English',
                    firstTime: widget.firstTime,
                  ),
                  SizedBox(
                    height: 10.h,
                  ),
                  SelectLang(
                    languageLocale: 'hi',
                    languageName: 'हिन्दी',
                    firstTime: widget.firstTime,
                  ),
                ],
              ),
            ),
          )
        : Scaffold(
            appBar: AppBar(
              title: Text(LocaleKeys.Common_Select_Langauge.tr()),
            ),
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SelectLang(
                    languageLocale: 'en',
                    languageName: 'English',
                    firstTime: widget.firstTime,
                  ),
                  SizedBox(height: 8.h),
                  SelectLang(
                    languageLocale: 'hi',
                    languageName: 'हिन्दी',
                    firstTime: widget.firstTime,
                  ),
                ],
              ),
            ),
          );
  }
}

class SelectLang extends StatelessWidget {
  final String languageLocale;
  final String languageName;
  final bool firstTime;
  const SelectLang({
    Key? key,
    required this.languageLocale,
    required this.languageName,
    required this.firstTime,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: () async {
        if (firstTime == false) {
          Navigator.of(context).pop();
          Navigator.of(context).pop();
        }
        Provider.of<AuthProvider>(context, listen: false)
            .languageSelected(languageLocale);
        // await Provider.of<AuthProvider>(context, listen: false)
        //     .updateLocalLanguage(languageLocale);
        await context.setLocale(
          Locale(languageLocale),
        );

        // callToast('Changing Language');
      },
      style: ButtonStyle(
        backgroundColor: MaterialStateProperty.all(mainColor),
        textStyle: MaterialStateProperty.all(
          const TextStyle(color: mainColor),
        ),
      ),
      child: Text(
        languageName,
        style: TextStyle(color: Colors.white),
      ),
    );
  }
}
