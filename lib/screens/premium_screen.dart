import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:zupid/translations/locale_keys.g.dart';
import 'package:provider/provider.dart';
import 'package:sizer/sizer.dart';
import 'package:zupid/screens/change_city_screen.dart';

import '../common_things/analytics.dart';
import '../common_things/my_colors.dart';

class PremiumScreen extends StatefulWidget {
  static const routeName = '/premium-screen';
  @override
  State<PremiumScreen> createState() => _PremiumScreenState();
}

class _PremiumScreenState extends State<PremiumScreen> {
  /////
  bool _isAnalytics = true;
  late final MyAnalytics myAnalytics;
  //////
  bool _isLoading = false;
  //
  @override
  Widget build(BuildContext context) {
    //////////
    if (_isAnalytics) {
      myAnalytics = Provider.of<MyAnalytics>(context, listen: false);
      myAnalytics.setLogEvent('PremiumScreen', {});
      _isAnalytics = false;
    }
    ////////////
    return Scaffold(
      appBar: AppBar(
        elevation: 0,
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: 3.h),
            Center(
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 5.w, vertical: 1.h),
                child: Text(
                  LocaleKeys.PremiumFeatures_zupid_Premium.tr(),
                  style: TextStyle(
                    color: mainContrastColor,
                    fontSize: 17.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
            SizedBox(height: 2.h),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 5.w, vertical: 1.h),
              child: Text(
                LocaleKeys
                        .PremiumFeatures_Enjoy_exclusive_access_to_following_features
                    .tr(),
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.bold,
                  color: thirdColorSoft,
                ),
              ),
            ),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 5.w, vertical: 1.h),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildFeatureItem(
                    LocaleKeys.PremiumFeatures_1_title.tr(),
                    LocaleKeys.PremiumFeatures_1_desc.tr(),
                  ),
                  _buildFeatureItem(
                    LocaleKeys.PremiumFeatures_2_title.tr(),
                    LocaleKeys.PremiumFeatures_2_desc.tr(),
                  ),
                  // _buildFeatureItem(
                  //   LocaleKeys.PremiumFeatures_3_title.tr(),
                  //   LocaleKeys.PremiumFeatures_3_desc.tr(),
                  // ),
                  _buildFeatureItem(
                    LocaleKeys.PremiumFeatures_4_title.tr(),
                    LocaleKeys.PremiumFeatures_4_desc.tr(),
                  ),
                  _buildFeatureItem(
                    LocaleKeys.PremiumFeatures_5_title.tr(),
                    LocaleKeys.PremiumFeatures_5_desc.tr(),
                  ),
                ],
              ),
            ),
            SizedBox(height: 2.h),
            Center(
              child: ElevatedButton(
                style: ButtonStyle(
                    padding: MaterialStateProperty.all(
                      EdgeInsets.symmetric(vertical: 1.5.h, horizontal: 10.w),
                    ),
                    shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                      RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(3.h),
                      ),
                    ),
                    backgroundColor:
                        MaterialStatePropertyAll(mainContrastColor)),
                onPressed: () {
                  Navigator.of(context).pushNamed(ChangeCityScreen.routeName);
                },
                child: Text(
                  LocaleKeys.PremiumFeatures_Change_your_city.tr(),
                  style: TextStyle(
                    color: mainColor,
                    fontSize: 12.5.sp,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

Widget _buildFeatureItem(String title, String description) {
  return Container(
    width: 100.w,
    child: Padding(
      padding: EdgeInsets.symmetric(vertical: 1.5.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            Icons.check_circle_outline,
            color: thirdColorSoft,
            size: 2.5.h,
          ),
          SizedBox(width: 2.h),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    color: thirdColorSoft,
                    fontSize: 12.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 0.5.h),
                Text(
                  description,
                  maxLines: 3,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(
                    color: thirdColorSoft,
                    fontSize: 10.sp,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    ),
  );
}
