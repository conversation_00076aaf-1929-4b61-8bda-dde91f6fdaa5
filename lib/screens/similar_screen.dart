import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:zupid/common_things/images.dart';
import 'package:zupid/translations/locale_keys.g.dart';
import 'package:zupid/widgets/display_user_info.dart';
import 'package:provider/provider.dart';
import 'package:sizer/sizer.dart';
import 'package:zupid/common_things/my_colors.dart';
import 'package:zupid/models/myUserPrefs.dart';
import 'package:zupid/screens/each_similar_screen.dart';
import 'package:zupid/widgets/loading_widget.dart';

import '../common_things/analytics.dart';
import '../common_things/my_fucntions.dart';
import '../models/myUserInfo.dart';
import '../providers/database_provider.dart';

class SimilarScreen extends StatefulWidget {
  static const routeName = '/similar-screen';

  @override
  State<SimilarScreen> createState() => _SimilarScreenState();
}

class _SimilarScreenState extends State<SimilarScreen> {
  /////
  bool _isAnalytics = true;
  late final MyAnalytics myAnalytics;
  bool dataLoadedMapCondition = false;
  //////
  int _currentPageIndex = 0;
  // later added //
  List<MyUserInfo>? similarProfilesList;
  //
  int? totalProfiles;
  MyUserInfo? _myUserInfo;
  MyUserPrefs? _myUserPrefs;
  //
  @override
  Widget build(BuildContext context) {
    //////////
    if (_isAnalytics) {
      myAnalytics = Provider.of<MyAnalytics>(context, listen: false);
      myAnalytics.setLogEvent('SimilarScreen', {});
      _isAnalytics = false;
    }
    ////////////
    final screenPadding = 1.w;
    double availableHeight = 100.h;
    final double screenWidth = 100.w - 2 * screenPadding;
    final double childWidth =
        screenWidth / 2; // divide by the number of columns
    final eachPhotoHeight = (availableHeight / 3);
    final double childAspectRatio = childWidth / eachPhotoHeight;
    // print(childAspectRatio);
    ////////////////////////////////////////////
//  ////// data loaded condition
    final _dataLoadedMap =
        Provider.of<DatabaseProvider>(context, listen: true).getDataLoadedMap;
    dataLoadedMapCondition = _dataLoadedMap['myUserInfo']! &&
        _dataLoadedMap['myUserPrefs']! &&
        _dataLoadedMap['similarScreen']!;
    dataLoadedMapCondition = _dataLoadedMap['loadall']!;

    ///
    if (dataLoadedMapCondition) {
      similarProfilesList = Provider.of<DatabaseProvider>(context, listen: true)
          .getSimilarProfilesList;
      //
      totalProfiles = similarProfilesList!.length;
      //
      _myUserInfo =
          Provider.of<DatabaseProvider>(context, listen: true).getMyUserInfo;
      _myUserPrefs =
          Provider.of<DatabaseProvider>(context, listen: true).getMyUserPrefs;
      //
    }

    final _promptList =
        Provider.of<DatabaseProvider>(context, listen: true).getPromptList;

    return dataLoadedMapCondition == false
        ? LoadingWidget()
        : totalProfiles == 0
            ? Padding(
                padding: EdgeInsets.all(10.w),
                child: Center(
                    child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    SizedBox(
                      height: 2.h,
                    ),
                    Text(
                      LocaleKeys.SimilarScreen_Recommended_Profiles.tr(),
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 4.w,
                      ),
                    ),
                    // SizedBox(
                    //   height: 25.h,
                    // ),
                    SizedBox(
                      height: 20.h,
                    ),
                    Container(
                      alignment: Alignment.center,
                      height: 10.h,
                      // width: 25.w,
                      child: Image.asset(
                        MyImages.kRose,
                      ),
                    ),
                    SizedBox(
                      height: 5.h,
                    ),
                    Text(
                      LocaleKeys.SimilarScreen_Searching_for_more.tr(),
                      style: TextStyle(
                          fontSize: 12.sp, fontWeight: FontWeight.bold),
                    ),
                    SizedBox(
                      height: 2.h,
                    ),
                    Text(
                      LocaleKeys.SimilarScreen_refreshed_every_week.tr(),
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 10.sp,
                      ),
                    ),
                  ],
                )),
              )
            : Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      mainColor,
                      // mainColor,

                      mainContrastColor.withOpacity(0.1),
                    ],
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                  ),
                ),
                child: Center(
                  child: Column(
                    children: [
                      SizedBox(
                        height: 6.h,
                      ),
                      Text(
                        LocaleKeys.SimilarScreen_Recommended_Profiles.tr(),
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          // color: mainColor,
                          fontSize: 4.w,
                        ),
                      ),
                      SizedBox(
                        height: 6.h,
                      ),
                      Container(
                        height: 60.h,
                        child: PageView.builder(
                          itemCount: similarProfilesList!.length,
                          controller: PageController(
                            initialPage: _currentPageIndex,
                            viewportFraction: 0.75,
                          ),
                          onPageChanged: (int index) {
                            setState(() {
                              _currentPageIndex = index;
                            });
                          },
                          itemBuilder: (BuildContext context, int index) {
                            MyUserInfo varUserInfo =
                                similarProfilesList![index];
                            List<String> _photos =
                                Provider.of<DatabaseProvider>(context,
                                        listen: true)
                                    .getUserSignedUrlMap[varUserInfo.userId]!;

                            int age = findAge(varUserInfo.dob);
                            int distance = distanceBetweenGeoLocator(
                                _myUserInfo!.latitude,
                                _myUserInfo!.longitude,
                                varUserInfo.latitude,
                                varUserInfo.longitude,
                                _myUserPrefs!);
                            //

                            return Container(
                              margin: EdgeInsets.fromLTRB(0, 0, 5.w, 0),
                              child: GestureDetector(
                                onTap: () {
                                  Navigator.of(context).pushNamed(
                                      EachSimilarScreen.routeName,
                                      arguments: [
                                        varUserInfo,
                                        index,
                                        _photos,
                                        'similar'
                                      ]);
                                },
                                child: Card(
                                  color: Colors.transparent,
                                  elevation: 2,
                                  child: Container(
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(1.w),
                                      border: Border.all(
                                          width: .1.w,
                                          color: mainContrastColor
                                              .withOpacity(0.1)),
                                      color: Colors.transparent,
                                    ),
                                    child: Center(
                                      child: SingleChildScrollView(
                                        child: Padding(
                                          padding: EdgeInsets.all(1.w),
                                          child: CompleteUserInfoDisplay(
                                            photoLink: _photos,
                                            varUserInfo: varUserInfo,
                                            age: age,
                                            distance: distance,
                                            column2Show: false,
                                            resizeFactor: .9,
                                            promptList: _promptList,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              );
  }
}
