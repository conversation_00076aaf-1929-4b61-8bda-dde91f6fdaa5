import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:zupid/translations/locale_keys.g.dart';
import 'package:provider/provider.dart';
import 'package:sizer/sizer.dart';
import 'package:zupid/common_things/my_colors.dart';
import 'package:zupid/widgets/loading_widget.dart';
import '../common_things/my_fucntions.dart';
import '../providers/database_provider.dart';

class BlockedReasonScreen extends StatefulWidget {
  static const routeName = '/blocked-reason';
  @override
  State<BlockedReasonScreen> createState() => _BlockedReasonScreenState();
}

class _BlockedReasonScreenState extends State<BlockedReasonScreen> {
  final _formKey = GlobalKey<FormState>();
  bool _isLoading = false;
  String blockedReason = '';

  @override
  Widget build(BuildContext context) {
    final args = ModalRoute.of(context)!.settings.arguments as List;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: mainColor,
        elevation: 0,
        iconTheme: IconThemeData(color: mainContrastColor),
        title: Text(
          LocaleKeys.BlockAndReportScreen_Report_user.tr(),
          style: TextStyle(
            color: mainContrastColor,
            fontSize: 16.sp,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
      body: _isLoading
          ? LoadingWidget()
          : Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    mainColor.withOpacity(0.9),
                    mainColor.withOpacity(0.95),
                    mainColor,
                  ],
                ),
              ),
              child: Center(
                child: SingleChildScrollView(
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: 6.w),
                    child: Card(
                      color: Colors.transparent,
                      elevation: 8,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(15),
                      ),
                      child: Padding(
                        padding: EdgeInsets.symmetric(
                            horizontal: 6.w, vertical: 4.h),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.report_problem_rounded,
                              size: 40.sp,
                              color: mainContrastColor,
                            ),
                            SizedBox(height: 3.h),
                            Text(
                              LocaleKeys
                                      .BlockAndReportScreen_Please_describe_your_reason_to_report_this_person
                                  .tr(),
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                color: mainContrastColor,
                                fontSize: 14.sp,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            SizedBox(height: 4.h),
                            Form(
                              key: _formKey,
                              child: TextFormField(
                                decoration: InputDecoration(
                                  filled: true,
                                  fillColor: Colors.black,
                                  labelStyle: TextStyle(
                                      color: mainContrastColor,
                                      fontSize: 12.sp),
                                  focusedBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(12),
                                    borderSide: BorderSide(
                                        color: mainContrastColor, width: 2),
                                  ),
                                  enabledBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(12),
                                    borderSide: BorderSide(
                                        color: mainContrastColor, width: 1),
                                  ),
                                  labelText: LocaleKeys
                                      .BlockAndReportScreen_Report_user.tr(),
                                  hintText: LocaleKeys
                                          .BlockAndReportScreen_Please_describe_your_reason_to_report_this_person
                                      .tr(),
                                  hintStyle: TextStyle(
                                      fontSize: 11.sp,
                                      color: mainContrastColor),
                                ),
                                style: TextStyle(fontSize: 12.sp),
                                textInputAction: TextInputAction.newline,
                                keyboardType: TextInputType.multiline,
                                maxLines: 5,
                                inputFormatters: [
                                  LengthLimitingTextInputFormatter(200),
                                ],
                                validator: (value) {
                                  if (value!.length < 20) {
                                    return 'Minimum 20 characters';
                                  }
                                  return null;
                                },
                                onSaved: (value) {
                                  blockedReason = value!;
                                },
                              ),
                            ),
                            SizedBox(height: 5.h),
                            SizedBox(
                              width: double.infinity,
                              child: ElevatedButton(
                                onPressed: () async {
                                  if (!_formKey.currentState!.validate()) {
                                    return;
                                  }
                                  _formKey.currentState!.save();

                                  setState(() {
                                    _isLoading = true;
                                  });
                                  final result =
                                      await Provider.of<DatabaseProvider>(
                                              context,
                                              listen: false)
                                          .blockChatDetails(
                                              args[0], args[1], blockedReason);
                                  setState(() {
                                    _isLoading = false;
                                  });
                                  Navigator.of(context).pop();
                                  Navigator.of(context).pop();
                                  if (result) {
                                    callToast(LocaleKeys
                                            .BlockAndReportScreen_Thank_you_for_keeping_zupid_safe
                                        .tr());
                                  } else {
                                    callToast(LocaleKeys
                                        .Common_Some_error_occured.tr());
                                  }
                                },
                                child: Padding(
                                  padding:
                                      EdgeInsets.symmetric(vertical: 1.5.h),
                                  child: Text(
                                    LocaleKeys.Common_Submit.tr(),
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 14.sp,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: mainContrastColor,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  elevation: 3,
                                ),
                              ),
                            ),
                            SizedBox(height: 2.h),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
    );
  }
}
