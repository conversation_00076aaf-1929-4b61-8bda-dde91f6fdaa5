import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:zupid/common_things/images.dart';
import 'package:zupid/translations/locale_keys.g.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:sizer/sizer.dart';

import 'package:zupid/common_things/my_colors.dart';
import 'package:zupid/common_things/my_fucntions.dart';
import 'package:zupid/models/myChat.dart';
import 'package:zupid/models/myChatDetails.dart';
import 'package:zupid/screens/blocked_reason_screen.dart';
import 'package:zupid/screens/user_profile_screen.dart';
import 'package:zupid/widgets/loading_widget.dart';

import '../common_things/analytics.dart';
import '../providers/database_provider.dart';

class ChatDetailsScreen extends StatefulWidget {
  static const routeName = '/chat-details-screen';

  @override
  State<ChatDetailsScreen> createState() => _ChatDetailsScreenState();
}

class _ChatDetailsScreenState extends State<ChatDetailsScreen> {
  // Initialize the TextEditingController
  final TextEditingController _messageController = TextEditingController();
  //
  /////
  bool _isAnalytics = true;
  late final MyAnalytics myAnalytics;
  //////
  bool _isLoading = false;
  bool _isMuted = false;
  bool _isBlocked = false;
  String varUserId = "-1";
  String chatDetailsId = "-1";
  int whichUser = -1;

  ///
  void _toggleMute() {
    setState(() {
      _isMuted = !_isMuted;
    });
  }

  void _toggleBlock() {
    setState(() {
      _isBlocked = !_isBlocked;
    });
  }

  //
  void initState() {
    // totalBullaChatsThereIs =
    //     Provider.of<DatabaseProvider>(context, listen: false).totalBullaChats;
    super.initState();
  }

  // //
  Future<void> loadData() async {
    // final results = await Provider.of<DatabaseProvider>(context, listen: false)
    //     .fetchBullaChat();
  }

  final RefreshController _refreshController =
      RefreshController(initialRefresh: false);

  void _onRefresh() async {
    // print('on refresh');
    // monitor network fetch
    await Future.delayed(Duration(milliseconds: 1000));
    await Provider.of<DatabaseProvider>(context, listen: false)
        .fetchChatsForUserId(varUserId, false);
    // if failed,use refreshFailed()
    _refreshController.refreshCompleted();
  }

  void _onLoading() async {
    // print('on loading');
    // monitor network fetch
    await Future.delayed(Duration(milliseconds: 1000));
    // if failed,use loadFailed(),if no data return,use LoadNodata()
    await Provider.of<DatabaseProvider>(context, listen: false)
        .fetchChatsForUserId(varUserId, false);
    // _bullaChats.add((items .length + 1).toString());
    if (mounted) setState(() {});
    _refreshController.loadComplete();
  }

  ///////
  List<MyChat> messages = [];
  ////
  final recieverColor = Colors.grey.shade200;
  final senderColor = Colors.black;
  final reciverTextColor = Colors.black.withOpacity(0.5);
  final senderTextColor = Colors.white.withOpacity(0.5);
  ////
  ///
  Widget _buildChatSettings(BuildContext context) {
    // load userinfo incase user want to see the profile,
    //already have codniton to avoide double load on fetchvaruserinfo func//
    return Wrap(
      children: [
        Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // SizedBox(height: 5.h),
            GestureDetector(
              onTap: () async {
                setState(() {
                  _isLoading = true;
                });
                await Provider.of<DatabaseProvider>(context, listen: false)
                    .unmatchChatDetails(chatDetailsId, whichUser);
                setState(() {
                  _isLoading = false;
                });
                Navigator.of(context).pop();
                Navigator.of(context).pop();
              },
              child: Container(
                color: mainColor,
                height: 7.h,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    SizedBox(width: 5.w),
                    Icon(Icons.heart_broken),
                    SizedBox(width: 5.w),
                    Text('Unmatch'),
                  ],
                ),
              ),
            ),
            // Divider(height: 0),
            GestureDetector(
              onTap: () async {
                Navigator.of(context).pop();
                Navigator.of(context).pushNamed(BlockedReasonScreen.routeName,
                    arguments: [chatDetailsId, whichUser]);
              },
              child: Container(
                color: mainColor,
                height: 7.h,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    SizedBox(width: 5.w),
                    Icon(Icons.block_rounded),
                    SizedBox(width: 5.w),
                    Text('Block And Report'),
                  ],
                ),
              ),
            ),
            // Divider(height: 0),
          ],
        ),
      ],
    );
  }

  int whichUserFunc(MyChatDetails _chatDetails, String userId) {
    // find mine userid inc har detaiuls
    if (_chatDetails.userId1 == userId) {
      return 2;
    } else {
      return 1;
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isAnalytics) {
      myAnalytics = Provider.of<MyAnalytics>(context, listen: false);
      myAnalytics.setLogEvent('ChatDetailsScreen', {});
      _isAnalytics = false;
    }
    //

    //
    final tempImageDirectory =
        Provider.of<DatabaseProvider>(context, listen: false)
            .tempDirectoryForImage;
    final args = ModalRoute.of(context)!.settings.arguments as List;
    //
    // MyChatDetails _myMatched = args[0];
    varUserId = args[0];
    MyChatDetails chatDetails = args[1];
    chatDetailsId = chatDetails.id;
    whichUser = whichUserFunc(chatDetails, varUserId);
    //
    List<String> _photos = Provider.of<DatabaseProvider>(context, listen: true)
        .getUserSignedUrlMap[varUserId]!;
    // get all chats for a userId
    messages = Provider.of<DatabaseProvider>(context, listen: true)
        .getAllChatsForAUserId(varUserId);
    //
    // print('message length: ${messages.length}');
    // update chat read in chat details
    //
    Provider.of<DatabaseProvider>(context, listen: false)
        .fetchVarUserInfo(varUserId);
    return PopScope(
      canPop: true,
      onPopInvoked: (did) async {
        await Provider.of<DatabaseProvider>(context, listen: false)
            .updateChatDetailsRead(chatDetailsId, whichUser);
        // return true;
      },
      child: Scaffold(
        backgroundColor: Color.lerp(mainContrastColor, Colors.black, 0.5),
        appBar: AppBar(
          elevation: 0,
          automaticallyImplyLeading: false,
          // backgroundColor: mainContrastColor.withOpacity(0.5),
          backgroundColor: Colors.transparent,

          iconTheme: const IconThemeData(color: Colors.black),
          flexibleSpace: SafeArea(
            child: Container(
              padding: EdgeInsets.only(right: 10.sp),
              child: Row(
                children: <Widget>[
                  IconButton(
                    onPressed: () {
                      Navigator.pop(context);
                    },
                    icon: Icon(
                      Icons.arrow_back,
                      color: Colors.black,
                    ),
                  ),
                  SizedBox(
                    width: 3.w,
                  ),
                  GestureDetector(
                    onTap: () async {
                      // setState(() {
                      //   _isLoading = true;
                      // });
                      await Provider.of<DatabaseProvider>(context,
                              listen: false)
                          .fetchVarUserInfo(varUserId);
                      // setState(() {
                      //   _isLoading = false;
                      // });

                      Navigator.of(context).pushNamed(
                          UserProfileScreen.routeName,
                          arguments: [varUserId, false]);
                    },
                    child: Center(
                      child: Container(
                        width: kToolbarHeight - 3.w,
                        height: kToolbarHeight - 3.w,
                        // width: 13.w, // Adjust width as needed
                        // height: 13.w, // Adjust height as needed
                        decoration: BoxDecoration(
                          shape: BoxShape.rectangle,
                          borderRadius:
                              BorderRadius.circular(12), // Rounded corners
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.1),
                              blurRadius: 8,
                              spreadRadius: 2,
                              offset: Offset(0, 2), // Shadow position
                            ),
                          ],
                        ),
                        child: ClipRRect(
                          borderRadius:
                              BorderRadius.circular(12), // Rounded corners
                          child: _photos[0] == "isLoading"
                              ? LoadingWidget()
                              : _photos.isNotEmpty && _photos[0] != "-1"
                                  ? Image.file(
                                      fit: BoxFit.cover,
                                      File(tempImageDirectory + _photos[0]))
                                  : Image.asset(
                                      "assets/images/no-photo.png",
                                      fit: BoxFit
                                          .cover, // Ensure image covers the container
                                    ),
                        ),
                      ),
                    ),
                  ),
                  SizedBox(
                    width: 5.w,
                  ),
                  Expanded(
                    child: Text(
                      capitalizeFirstLetter(chatDetails.firstName),
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                          fontSize: 13.sp,
                          fontWeight: FontWeight.w600,
                          color: thirdColor),
                    ),
                  ),
                  IconButton(
                    onPressed: () {
                      showModalBottomSheet(
                        context: context,
                        builder: (BuildContext context) {
                          return _buildChatSettings(context);
                        },
                      );
                    },
                    icon: const Icon(
                      Icons.settings,
                      color: Colors.black,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        body: _isLoading
            ? LoadingWidget()
            : SmartRefresher(
                enablePullDown: false,
                enablePullUp: true,
                header: WaterDropHeader(),
                footer: CustomFooter(
                  builder: (BuildContext context1, LoadStatus? mode) {
                    Widget body;
                    // if (totalBullaChatsThereIs == _bullaChats.length) {
                    //   body = Text(
                    //     "No more opinion to load",
                    //     style: TextStyle(fontSize: 10.sp, color: Colors.grey),
                    //   );
                    // }
                    if (mode == LoadStatus.loading) {
                      body = LoadingWidget();
                    } else if (mode == LoadStatus.failed) {
                      body = const Text("Load Failed!Click retry!");
                    } else if (mode == LoadStatus.canLoading) {
                      body = const Text("release to load more");
                    } else {
                      body = const Text("No more messages");
                    }
                    return Container(
                      height: 55.0,
                      child: Center(child: body),
                    );
                  },
                ),
                controller: _refreshController,
                onRefresh: _onRefresh,
                onLoading: _onLoading,
                child: ListView.builder(
                  reverse: true,
                  // physics: ScrollPhysics(),
                  padding: EdgeInsets.only(top: 2.5.h, bottom: 2.5.h),
                  shrinkWrap: true,
                  itemCount:
                      messages.length + 1, // +1 to show progress indicator.
                  itemBuilder: (context, index) {
                    if (index == 0) {
                      return AddMessageWidget(
                        myController: _messageController,
                        varUserId: varUserId,
                        chatDetailsId: chatDetailsId,
                        whichUser: whichUser,
                      );
                    }
                    var varMessage = messages[index - 1];
                    return Container(
                      color: Colors.transparent,
                      padding: EdgeInsets.only(
                          left: varMessage.senderId == varUserId ? 5.w : 33.w,
                          right: varMessage.senderId == varUserId ? 33.w : 5.w,
                          top: 2.w,
                          bottom: 2.w),
                      child: Align(
                        alignment: (varMessage.senderId == varUserId
                            ? Alignment.bottomLeft
                            : Alignment.bottomRight),
                        child: Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(20),
                            color: (varMessage.senderId == varUserId
                                ? Colors.grey.shade200.withOpacity(0.7)
                                : Colors.black.withOpacity(0.7)),
                          ),
                          padding: EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: varMessage.senderId == varUserId
                                ? CrossAxisAlignment.start
                                : CrossAxisAlignment.end,
                            children: [
                              Text(
                                varMessage.message,
                                style: TextStyle(
                                  fontSize: 15,
                                  color: (varMessage.senderId == varUserId
                                      ? reciverTextColor
                                      : senderTextColor),
                                ),
                              ),
                              SizedBox(
                                height: .5.h,
                              ),
                              Text(
                                chatDateTime(varMessage.createdAt),
                                style: TextStyle(
                                  fontSize: 9,
                                  color: (varMessage.senderId == varUserId)
                                      ? Colors.black
                                      : Colors.grey,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
      ),
    );
  }
}

class AddMessageWidget extends StatelessWidget {
  final TextEditingController myController;
  final String varUserId;
  final String chatDetailsId;
  final int whichUser;
  AddMessageWidget({
    required this.myController,
    required this.varUserId,
    required this.chatDetailsId,
    required this.whichUser,
  });

  int _lineCount = 1;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.fromLTRB(3.w, 5.w, 3.w, 0),
      child: Container(
        height: 10.h,
        // height: _lineCount <= 1
        //     ? 10.h
        //     : (_lineCount * 5.h).clamp(10.h, 20.h), // Adjust height
        width: double.infinity,
        decoration: BoxDecoration(
          border:
              Border.all(color: mainContrastColor.withOpacity(0.8), width: 1),
          borderRadius: BorderRadius.circular(2.h),
        ),
        // color: mainContrastColor.withOpacity(0.3),
        child: Row(
          children: <Widget>[
            SizedBox(width: 5.w),
            Expanded(
              child: TextField(
                maxLines: 4, // Maximum number of lines
                // minLines: 1, // Minimum number of lines
                // minLines: 3, // Minimum number of lines (initial size)
                decoration: InputDecoration(
                  hintText: LocaleKeys.Common_Write_message.tr(),
                  hintStyle: TextStyle(color: Colors.black54),
                  border: InputBorder.none,
                ),
                style: TextStyle(
                    color: mainColor.withOpacity(0.8),
                    fontWeight: FontWeight.w500), // Set typing color to black
                controller: myController,
                keyboardType: TextInputType.text,
                onSubmitted: (value) {
                  // print(value);
                  if (containsOnlySpaces(value)) {
                    LocaleKeys.Common_enter_something.tr();
                  } else {
                    myController.text = value;
                  }
                  // Do something when the user types in the text field
                },
              ),
            ),
            SizedBox(width: 5.w),
            Padding(
              padding: EdgeInsets.fromLTRB(2.h, 2.h, 1.h, 2.h),
              child: FloatingActionButton(
                onPressed: () async {
                  String message = myController.text;
                  // print(message);
                  if (containsOnlySpaces(message)) {
                    callToast(LocaleKeys.Common_enter_something.tr());
                    myController.text = '';
                    return;
                  }
                  //
                  myController.text = "";
                  //
                  await Provider.of<DatabaseProvider>(context, listen: false)
                      .addAChat(varUserId, message, chatDetailsId, whichUser);
                  //
                },
                child: Icon(
                  Icons.send,
                  color: mainColor,
                ),
                backgroundColor: mainContrastColor,
                elevation: 0,
              ),
            ),
            SizedBox(width: 2.w),
          ],
        ),
      ),
    );
  }
}
