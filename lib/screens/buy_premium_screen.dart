import 'dart:math';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:zupid/translations/locale_keys.g.dart';
import 'package:payu_checkoutpro_flutter/payu_checkoutpro_flutter.dart';
import 'package:provider/provider.dart';
import 'package:sizer/sizer.dart';
import 'package:zupid/models/myUserInfo.dart';
import 'package:zupid/screens/transaction_status_screen.dart';

import '../common_things/analytics.dart';
import '../common_things/my_colors.dart';
import '../common_things/my_payu.dart';
import '../providers/auth_provider.dart';
import '../providers/database_provider.dart';

class BuyPremiumScreen extends StatefulWidget {
  static const routeName = '/buy-premium-screen';

  @override
  _BuyPremiumScreenState createState() => _BuyPremiumScreenState();
}

class _BuyPremiumScreenState extends State<BuyPremiumScreen>
    implements PayUCheckoutProProtocol {
  late PayUCheckoutProFlutter _checkoutPro;
  //
  bool _isAnalytics = true;
  late final MyAnalytics myAnalytics;
  //
  double amount = 0;
  String _transactionId = '';
  String _userName = '';
  int totalMonths = 0;
  int monthlyPrice = 0;
  String _userId = "";
  int _phoneNumber = 0;

  void createRandomOrderId() {
    const _chars =
        'AaBbCcDdEeFfGgHhIiJjKkLlMmNnOoPpQqRrSsTtUuVvWwXxYyZz1234567890';
    Random _rnd = Random();
    String getRandomString(int length) =>
        String.fromCharCodes(Iterable.generate(
            length, (_) => _chars.codeUnitAt(_rnd.nextInt(_chars.length))));
    _transactionId = getRandomString(10);
    _transactionId = 'payu_' + _transactionId;
    // print(_transactionId);
  }

  //
  @override
  void initState() {
    super.initState();
    _checkoutPro = PayUCheckoutProFlutter(this);
  }

  int _selectedDuration = 2;

  List<dynamic> _pricingOptions = [];

  ///
  void _showPaymentDialog(MyUserInfo _myUserInfo, int phoneNumber) {
    // print('show payment dialog');
    final option = _pricingOptions[_selectedDuration];
    totalMonths = option['months'];
    monthlyPrice = option['price'];
    //
    int subTotal = option['price'] * totalMonths;
    int gst = (subTotal * 0.18) ~/ 1;
    int totalAmount = (subTotal + gst) ~/ 1;
    //
    amount = double.parse(totalAmount.toString());
    //

    showDialog(
      context: context,
      builder: (BuildContext context) => Dialog(
        child: Padding(
          padding: EdgeInsets.all(3.h),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                LocaleKeys.BuyPremiumScreen_Payment_Details.tr(),
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: 4.h),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                      '${LocaleKeys.BuyPremiumScreen_Subscription_Fee.tr()} (${option['duration']}) x $totalMonths'),
                  Text('\u{20B9} $subTotal'),
                ],
              ),
              SizedBox(height: 2.h),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text('GST (18%)'),
                  Text('\u{20B9} $gst'),
                ],
              ),
              SizedBox(height: 4.h),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    LocaleKeys.BuyPremiumScreen_Total_Amount.tr(),
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    '\u{20B9} ${totalAmount.toStringAsFixed(2)}',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              SizedBox(height: 4.h),
              ElevatedButton(
                style: ButtonStyle(
                  backgroundColor: MaterialStateProperty.all(mainColor),
                ),
                onPressed: () {
                  // Navigator.pop(context);
                  ///////////////////////////////////
                  print('creating random order id');
                  createRandomOrderId();
                  print('opening checkput screen');

                  _checkoutPro.openCheckoutScreen(
                    payUPaymentParams: {
                      PayUPaymentParamKey.key: "Y7IS1U",
                      PayUPaymentParamKey.amount: amount.toString(),
                      PayUPaymentParamKey.productInfo: "zupid Premium",
                      PayUPaymentParamKey.firstName: _userName,
                      PayUPaymentParamKey.email: "<EMAIL>",
                      PayUPaymentParamKey.phone: _phoneNumber.toString(),
                      PayUPaymentParamKey.environment: "0",
                      // String - "0" for Production and "1" for Test
                      PayUPaymentParamKey.transactionId: _transactionId,
                      // transactionId Cannot be null or empty and should be unique for each transaction. Maximum allowed length is 25 characters. It cannot contain special characters like: -_/
                      PayUPaymentParamKey.userCredential: "Y7IS1U:$_userId",
                      //  Format: <merchantKey>:<userId> ... UserId is any id/email/phone number to uniquely identify the user.
                      PayUPaymentParamKey.android_surl:
                          "https:///www.payumoney.com/mobileapp/payumoney/success.php",
                      PayUPaymentParamKey.android_furl:
                          "https:///www.payumoney.com/mobileapp/payumoney/failure.php",
                      PayUPaymentParamKey.ios_surl:
                          "https:///www.payumoney.com/mobileapp/payumoney/success.php",
                      PayUPaymentParamKey.ios_furl:
                          "https:///www.payumoney.com/mobileapp/payumoney/failure.php",
                    },
                    payUCheckoutProConfig: PayUParams.createPayUConfigParams(),
                  );
                  print('opening checkput screen: DONE');

                  Navigator.of(context).pop();
                },
                child: Text(
                  LocaleKeys.BuyPremiumScreen_Pay_Now.tr(),
                  style: const TextStyle(color: Colors.white),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    //analytics
    if (_isAnalytics) {
      myAnalytics = Provider.of<MyAnalytics>(context, listen: false);
      myAnalytics.setLogEvent('BuyPremiumScreen', {});
      _isAnalytics = false;
    }
    //
    MyUserInfo _myUserInfo =
        Provider.of<DatabaseProvider>(context, listen: false).getMyUserInfo;
    _userName = _myUserInfo.firstName;
    _userId = _myUserInfo.userId;
    _phoneNumber =
        Provider.of<DatabaseProvider>(context, listen: false).getMyPhoneNumber;
    //
    _pricingOptions =
        Provider.of<AuthProvider>(context, listen: false).getMembershipPlans;
    return Scaffold(
      appBar: AppBar(
        actions: [
          Padding(
            padding: EdgeInsets.all(2.w),
            child: ElevatedButton(
              style: const ButtonStyle(
                backgroundColor: MaterialStatePropertyAll(
                  mainColor,
                ),
              ),
              onPressed: () {
                //
                print('show payment dialog: run');

                int phoneNumber =
                    Provider.of<DatabaseProvider>(context, listen: false)
                        .getMyPhoneNumber;
                _showPaymentDialog(_myUserInfo, phoneNumber);
                print('show payment dialog: done');
              },
              child: Text(
                LocaleKeys.BuyPremiumScreen_Buy_Now.tr(),
                style: const TextStyle(color: Colors.white),
              ),
            ),
          )
        ],
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: 3.h),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 5.w, vertical: 1.h),
            child: Text(
              LocaleKeys.BuyPremiumScreen_Upgrade_to_zupid_Premium.tr(),
              style: TextStyle(
                  fontSize: 17.sp,
                  fontWeight: FontWeight.bold,
                  color: mainColor),
            ),
          ),
          SizedBox(height: 2.h),

          // Padding(
          //   padding: EdgeInsets.symmetric(horizontal: 5.w, vertical: 1.h),
          //   child: Column(
          //     crossAxisAlignment: CrossAxisAlignment.start,
          //     children: [
          //       _buildFeatureItem(
          //         'Change your city',
          //         'Update your location to match with people in different cities',
          //       ),
          //       _buildFeatureItem(
          //         'Swipe unlimited matches',
          //         'Swipe as much as you want with no limits',
          //       ),
          //       _buildFeatureItem(
          //         '10 Super likes matches per day',
          //         'Unlock the power of Super likes to stand out and make a memorable impression on potential matches',
          //       ),
          //       _buildFeatureItem(
          //         'More visibility',
          //         'Get more exposure in search results and boost your profile',
          //       ),
          //     ],
          //   ),
          // ),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 5.w, vertical: 1.h),
            child: Text(
              LocaleKeys.BuyPremiumScreen_Choose_a_Subscription.tr(),
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          SizedBox(height: 2.h),
          Expanded(
            child: ListView.builder(
              itemCount: _pricingOptions.length,
              itemBuilder: (context, index) {
                final isSelected = _selectedDuration == index;
                final option = _pricingOptions[index];
                final savings = option['savings'];
                return GestureDetector(
                  onTap: () {
                    setState(() {
                      _selectedDuration = index;
                    });
                  },
                  child: Container(
                    margin:
                        EdgeInsets.symmetric(horizontal: 5.w, vertical: 1.h),
                    padding:
                        EdgeInsets.symmetric(horizontal: 5.w, vertical: 2.h),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(2.5.h),
                      color: isSelected
                          ? mainColor.withOpacity(0.9)
                          : Colors.grey[300],
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          option['duration'],
                          style: TextStyle(
                            color: isSelected ? Colors.white : Colors.black,
                            fontSize: 16.sp,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        SizedBox(height: 1.h),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              '\u{20B9} ${option['price']}/${LocaleKeys.BuyPremiumScreen_month.tr()}',
                              style: TextStyle(
                                color: isSelected ? Colors.white : Colors.black,
                              ),
                            ),
                            if (savings > 0)
                              Text(
                                '${LocaleKeys.BuyPremiumScreen_Save.tr()} $savings%',
                                style: TextStyle(
                                  color: isSelected
                                      ? Colors.white
                                      : Colors.black54,
                                  fontSize: 11.sp,
                                  fontWeight: FontWeight.w700,
                                ),
                              ),
                          ],
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  ///////////////////////////////////////////////
  ///////////////////////////////////////////////
  @override
  generateHash(Map response) async {
    Map hashResponse = {};
    // _checkoutPro.hashGenerated(hash: hashResponse);

    int currentTime =
        DateTime.now().millisecondsSinceEpoch ~/ 1000; // value in seconds

    hashResponse = await Provider.of<DatabaseProvider>(context, listen: false)
        .fetchPayuHash(
            _userName,
            _transactionId,
            amount,
            response[PayUHashConstantsKeys.hashName],
            response[PayUHashConstantsKeys.hashString],
            totalMonths,
            monthlyPrice);

    _checkoutPro.hashGenerated(hash: hashResponse);
  }

  @override
  onPaymentSuccess(dynamic response) {
    Navigator.of(context).pushNamed(
      TransactionStatusScreen.routeName,
      arguments: ['Success'],
    );
    // showAlertDialog(context, "onPaymentSuccess", response.toString());
  }

  @override
  onPaymentFailure(dynamic response) {
    Navigator.of(context).pushNamed(
      TransactionStatusScreen.routeName,
      arguments: ['Failed'],
    );

    // showAlertDialog(context, "onPaymentFailure", response.toString());
  }

  @override
  onPaymentCancel(Map? response) {
    Navigator.of(context).pushNamed(
      TransactionStatusScreen.routeName,
      arguments: ['Cancel'],
    );
    // showAlertDialog(context, "onPaymentCancel", response.toString());
  }

  @override
  onError(Map? response) {
    Navigator.of(context).pushNamed(
      TransactionStatusScreen.routeName,
      arguments: ['Error'],
    );
    // showAlertDialog(context, "onError", response.toString());
  }
}

Widget _buildFeatureItem(String title, String description) {
  return Container(
    width: 100.w,
    child: Padding(
      padding: EdgeInsets.symmetric(vertical: 1.5.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            Icons.check_circle_outline,
            color: mainColor,
            size: 2.5.h,
          ),
          SizedBox(width: 2.h),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    color: Colors.black,
                    fontSize: 12.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 0.5.h),
                Text(
                  description,
                  maxLines: 3,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(
                    color: Colors.black54,
                    fontSize: 10.sp,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    ),
  );
}
