import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:zupid/common_things/images.dart';
import 'package:zupid/translations/locale_keys.g.dart';
import 'package:provider/provider.dart';
import 'package:sizer/sizer.dart';
import 'package:zupid/common_things/my_colors.dart';
import 'package:zupid/common_things/my_fucntions.dart';
import 'package:zupid/models/myChatDetails.dart';
import 'package:zupid/models/myUserInfo.dart';
import 'package:zupid/providers/database_provider.dart';
import 'package:zupid/screens/chat_details_screen.dart';
import 'package:zupid/widgets/loading_widget.dart';

import '../common_things/analytics.dart';

class ChatScreen extends StatefulWidget {
  static const routeName = '/chat-screen';

  @override
  State<ChatScreen> createState() => _ChatScreenState();
}

class _ChatScreenState extends State<ChatScreen> {
  /////
  bool _isAnalytics = true;
  late final MyAnalytics myAnalytics;
  //////
  bool _isLoading = false;
  //
  List<MyChatDetails> _myMatchedChatDetailsList = [];
  List<MyChatDetails> _onlyMatchedUsers = [];
  List<MyChatDetails> _onlyChattedUsers = [];
  // find only matched
  void findOnlyMatched() {
    _onlyMatchedUsers = _myMatchedChatDetailsList
        .where((element) => element.lastMessage == "")
        .toList();
    // print('Not chated yet: ${_onlyMatchedUsers.length}');
  }

  void findOnlyChatted() {
    _onlyChattedUsers = _myMatchedChatDetailsList
        .where((element) => element.lastMessage != "")
        .toList();
    // Sort the list by updated_at time in descending order
    _onlyChattedUsers
        .sort((a, b) => b.lastMessageTime.compareTo(a.lastMessageTime));
    // print('chated : ${_onlyChattedUsers.length}');
  }

  @override
  Widget build(BuildContext context) {
    if (_isAnalytics) {
      myAnalytics = Provider.of<MyAnalytics>(context, listen: false);
      myAnalytics.setLogEvent('ChatScreen', {});
      _isAnalytics = false;
    }
    //
    final tempImageDirectory =
        Provider.of<DatabaseProvider>(context, listen: false)
            .tempDirectoryForImage;
    _myMatchedChatDetailsList =
        Provider.of<DatabaseProvider>(context, listen: true)
            .getMatchedChatDetailsList;
    //
    findOnlyChatted();
    findOnlyMatched();
    //
    final _myUserInfo =
        Provider.of<DatabaseProvider>(context, listen: true).getMyUserInfo;

    // print('chat screen update ping.....');
    return _myMatchedChatDetailsList.length == 0
        ? Padding(
            padding: EdgeInsets.all(5.w),
            child: Column(
              children: [
                SizedBox(
                  height: 20.h,
                ),
                Container(
                  alignment: Alignment.center,
                  height: 10.h,
                  // width: 25.w,
                  child: Image.asset(
                    MyImages.kRose,
                  ),
                ),
                SizedBox(
                  height: 5.h,
                ),
                Text(
                  "No matches yet",
                  style:
                      TextStyle(fontSize: 12.sp, fontWeight: FontWeight.bold),
                ),
                SizedBox(
                  height: 2.h,
                ),
                Text(
                  'Keep sending messages and updating your profile to find someone special!',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 10.sp,
                  ),
                ),
              ],
            ),
          )
        : _isLoading
            ? LoadingWidget()
            : SingleChildScrollView(
                physics: const ScrollPhysics(),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(height: 2.h),
                    if (_onlyMatchedUsers.isNotEmpty)
                      Container(
                        height: 30.w, // Adjusted height for better aesthetics
                        child: ListView.builder(
                          physics: const BouncingScrollPhysics(),
                          scrollDirection: Axis.horizontal,
                          itemCount: _onlyMatchedUsers.length,
                          itemBuilder: (ctx, index) {
                            String userId = findVarUserIdFromChatDetails(
                                _onlyMatchedUsers[index], _myUserInfo.userId);
                            List<String> _photos =
                                Provider.of<DatabaseProvider>(context,
                                        listen: true)
                                    .getUserSignedUrlMap[userId]!;

                            return GestureDetector(
                              onTap: () async {
                                // setState(() {
                                //   _isLoading = true;
                                // });
                                // await Provider.of<DatabaseProvider>(context,
                                //         listen: false)
                                //     .fetchChatsForUserId(userId, true);
                                // setState(() {
                                //   _isLoading = false;
                                // });
                                Navigator.of(context).pushNamed(
                                    ChatDetailsScreen.routeName,
                                    arguments: [
                                      userId,
                                      _onlyMatchedUsers[index]
                                    ]);
                              },
                              child: Container(
                                width: 27.w,
                                child: Card(
                                  elevation: 1,
                                  color: mainContrastColor.withOpacity(0.6),
                                  surfaceTintColor: Colors.white,
                                  child: Padding(
                                    padding:
                                        EdgeInsets.symmetric(horizontal: 1.w),
                                    child: Column(
                                      children: [
                                        ClipRRect(
                                          borderRadius: BorderRadius.vertical(
                                              top: Radius.circular(1.w)),
                                          child: _photos[0] == "isLoading"
                                              ? LoadingWidget()
                                              : _photos.isNotEmpty &&
                                                      _photos[0] != "-1"
                                                  ? Container(
                                                      height: 18.w,
                                                      width: 18.w,
                                                      child: Image.file(File(
                                                          tempImageDirectory +
                                                              _photos[0])))
                                                  : Container(
                                                      height: 18.w,
                                                      width: 18.w,
                                                      child: Image.asset(
                                                          "assets/images/no-photo.png"),
                                                    ),
                                        ),
                                        SizedBox(height: 2.w),
                                        Padding(
                                          padding: EdgeInsets.symmetric(
                                              horizontal: 1.w),
                                          child: Text(
                                            capitalizeFirstLetter(
                                                _onlyMatchedUsers[index]
                                                    .firstName),
                                            overflow: TextOverflow.ellipsis,
                                            maxLines:
                                                1, // Maximum number of lines before truncation
                                            style: TextStyle(
                                              fontSize: 11.sp,
                                              color: thirdColor,
                                              fontWeight: FontWeight.w500,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                      ),

                    if (_onlyMatchedUsers.isNotEmpty)
                      Column(
                        children: [
                          SizedBox(height: 1.h),
                          Divider(
                            thickness: 1.sp,
                            height: 0,
                            color: mainColor,
                          ),
                          SizedBox(height: 1.h),
                        ],
                      ),

                    // The rest of the code for active messages
                    ListView.builder(
                      physics: const NeverScrollableScrollPhysics(),
                      shrinkWrap: true,
                      itemCount: _onlyChattedUsers.length,
                      itemBuilder: (ctx, index) {
                        final chat = _onlyChattedUsers[index];
                        // print('${chat.firstName} ${chat.lastMessageTime}');
                        String userId = findVarUserIdFromChatDetails(
                            _onlyChattedUsers[index], _myUserInfo.userId);
                        List<String> _photos =
                            Provider.of<DatabaseProvider>(context, listen: true)
                                .getUserSignedUrlMap[userId]!;

                        int unread = userId == _onlyChattedUsers[index].userId1
                            ? _onlyChattedUsers[index].unread2
                            : _onlyChattedUsers[index].unread1;

                        return GestureDetector(
                          onTap: () async {
                            // setState(() {
                            //   _isLoading = true;
                            // });
                            // await Provider.of<DatabaseProvider>(context,
                            //         listen: false)
                            //     .fetchChatsForUserId(userId, true);
                            Navigator.of(context).pushNamed(
                                ChatDetailsScreen.routeName,
                                arguments: [userId, _onlyChattedUsers[index]]);
                            // setState(() {
                            //   _isLoading = false;
                            // });
                          },
                          child: Container(
                            color: mainContrastColor.withOpacity(0.5),
                            margin: EdgeInsets.symmetric(vertical: 0.2.h),
                            child: ListTile(
                              contentPadding: EdgeInsets.symmetric(
                                  horizontal: 2.w, vertical: 0.2.h),
                              leading: Container(
                                width: 13.w, // Adjust width as needed
                                height: 13.w, // Adjust height as needed
                                decoration: BoxDecoration(
                                  shape: BoxShape.rectangle,
                                  borderRadius: BorderRadius.circular(
                                      12), // Rounded corners
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.black.withOpacity(0.1),
                                      blurRadius: 8,
                                      spreadRadius: 2,
                                      offset: Offset(0, 2), // Shadow position
                                    ),
                                  ],
                                ),
                                child: ClipRRect(
                                  borderRadius: BorderRadius.circular(
                                      12), // Rounded corners
                                  child: _photos[0] == "isLoading"
                                      ? LoadingWidget()
                                      : _photos.isNotEmpty && _photos[0] != "-1"
                                          ? Image.file(
                                              fit: BoxFit.cover,
                                              File(tempImageDirectory +
                                                  _photos[0]))
                                          : Image.asset(
                                              "assets/images/no-photo.png",
                                              fit: BoxFit
                                                  .cover, // Ensure image covers the container
                                            ),
                                ),
                              ),
                              title: Row(
                                children: [
                                  Expanded(
                                    child: Text(
                                      chat.firstName,
                                      overflow: TextOverflow.ellipsis,
                                      style: TextStyle(
                                        fontSize: 13.sp,
                                        color: thirdColor,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ),
                                  if (unread > 0)
                                    Container(
                                      padding: EdgeInsets.symmetric(
                                          horizontal: 6.sp, vertical: 2.sp),
                                      decoration: BoxDecoration(
                                        color: mainColor,
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                      child: Text(
                                        unread.toString(),
                                        style: TextStyle(
                                          fontSize: 10.sp,
                                          color: Colors.white,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ),
                                ],
                              ),
                              subtitle: Text(
                                chat.lastMessage.isEmpty
                                    ? LocaleKeys.ChatScreen_Start_conversation
                                        .tr()
                                    : chat.lastMessage,
                                overflow: TextOverflow.ellipsis,
                                style: TextStyle(
                                  color: Colors.grey,
                                  fontSize: 12.sp,
                                  fontWeight: unread == 0
                                      ? FontWeight.normal
                                      : FontWeight.bold,
                                ),
                              ),
                              trailing: Text(
                                chat.lastMessage.isEmpty
                                    ? ""
                                    : formatDateTime(chat.lastMessageTime),
                                overflow: TextOverflow.ellipsis,
                                style: TextStyle(
                                  color: Colors.black,
                                  fontSize: 10.sp,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                  ],
                ),
              );
  }
}
