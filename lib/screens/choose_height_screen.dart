import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:zupid/common_things/my_constants.dart';
import 'package:zupid/providers/database_provider.dart';
import 'package:provider/provider.dart';
import 'package:sizer/sizer.dart';

import 'package:zupid/common_things/my_colors.dart';
import 'package:zupid/providers/auth_provider.dart';
import 'package:zupid/translations/locale_keys.g.dart';
import 'package:wheel_picker/wheel_picker.dart';

class ChooseHeightScreen extends StatefulWidget {
  final bool isHeightCheck;
  static const routename = '/choose-height-screen';

  const ChooseHeightScreen({super.key, required this.isHeightCheck});

  @override
  State<ChooseHeightScreen> createState() => _ChooseHeightScreenState();
}

class _ChooseHeightScreenState extends State<ChooseHeightScreen> {
  bool _isLoading = false;
  int selectedHeight = 160;
  List<int> generateNumbers(int x, int y) {
    return List.generate(y - x + 1, (index) => x + index);
  }

  @override
  Widget build(BuildContext context) {
    final height = Provider.of<AuthProvider>(context, listen: false).getHeight;
    final heightList = generateNumbers(height[0], height[1]);
    return WillPopScope(
      onWillPop: (() async {
        setState(() {
          _isLoading = true;
        });
        //
        // print(selectedHeight);
        Map<String, String> _payloadData = {
          'height': selectedHeight.toString(),
        };
        Provider.of<DatabaseProvider>(context, listen: false)
            .updateUserInfo(userInfoHeightRoute, _payloadData);
        //
        setState(() {
          _isLoading = false;
        });
        return true;
      }),
      child: Scaffold(
        appBar: AppBar(
            // title: Center(child: ),
            ),
        body: SingleChildScrollView(
          child: Column(
            children: [
              SizedBox(
                height: 2.h,
              ),
              Text(
                'Select Height',
                style: TextStyle(fontSize: 18.sp, fontWeight: FontWeight.bold),
              ),
              Container(
                // color: mainContrastColor,
                height: 50.h,
                child: Center(
                  child: WheelPicker(
                    style: WheelPickerStyle(
                      itemExtent: 25.sp,
                    ),
                    initialIndex: 40,
                    itemCount: heightList.length,
                    builder: (context, index) => Text(
                      '${heightList[index].toString()} cm',
                      style: TextStyle(fontSize: 20.sp),
                    ),
                    selectedIndexColor: Colors.white,
                    looping: false,
                    onIndexChanged: (index) {
                      setState(() {
                        selectedHeight =
                            heightList[index]; // Update selected height
                      });
                    },
                  ),
                ),
              ),
              if (widget.isHeightCheck)
                SizedBox(
                  height: 5.h,
                ),
              if (widget.isHeightCheck)
                ElevatedButton(
                  onPressed: () async {
                    setState(() {
                      _isLoading = true;
                    });
                    //
                    // print(selectedHeight);
                    Map<String, String> _payloadData = {
                      'height': selectedHeight.toString(),
                    };
                    Provider.of<DatabaseProvider>(context, listen: false)
                        .updateUserInfo(userInfoHeightRoute, _payloadData);
                    //
                    setState(() {
                      _isLoading = false;
                    });
                  },
                  child: Text(
                    'Next',
                    style: TextStyle(color: mainColor),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
