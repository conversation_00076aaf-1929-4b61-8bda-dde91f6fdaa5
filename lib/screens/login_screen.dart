import 'dart:async';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:zupid/common_things/images.dart';
import 'package:zupid/common_things/my_colors.dart';
import 'package:zupid/translations/locale_keys.g.dart';
import 'package:provider/provider.dart';
import 'package:sizer/sizer.dart';
import 'package:zupid/providers/auth_provider.dart';
import 'package:zupid/widgets/loading_widget.dart';

import '../common_things/analytics.dart';

class LoginScreen extends StatefulWidget {
  static const routeName = '/login-screen';

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  /////
  bool _isAnalytics = true;
  late final MyAnalytics myAnalytics;
  //////
  bool _isLoading = false;
  final _formKey = GlobalKey<FormState>();
  final _otpKey = GlobalKey<FormState>();
  Timer? _timer;
  final int countdown = 10;
  int _start = 10;
  bool isSend = false;
  String? _phoneNumber;
  String? _otp;
  bool? sendResult;
  //
  @override
  Widget build(BuildContext context) {
    // Provider.of<AuthProvider>(context, listen: false).login(**********);
    //////////
    if (_isAnalytics) {
      myAnalytics = Provider.of<MyAnalytics>(context, listen: false);
      myAnalytics.setLogEvent('LoginScreen', {});
      _isAnalytics = false;
    }
    ////////////
    return Scaffold(
      body: _isLoading == true
          ? LoadingWidget()
          : SingleChildScrollView(
              child: Container(
                height: 100.h,
                child: Padding(
                  padding: EdgeInsets.fromLTRB(10.w, 0, 10.w, 0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Column(
                        children: [
                          SizedBox(
                            height: 20.h,
                          ),
                          Container(
                            alignment: Alignment.center,
                            width: 90.w,
                            child: Image.asset(
                              MyImages.k_500_500,
                            ),
                          ),
                          // SizedBox(
                          //   height: 5.h,
                          // ),
                          // Row(
                          //   mainAxisAlignment: MainAxisAlignment.center,
                          //   children: [
                          //     Text(
                          //       LocaleKeys.LoginScreen_Created_in.tr(),
                          //       style: TextStyle(
                          //           color: Colors.grey,
                          //           fontSize: 4.w,
                          //           fontWeight: FontWeight.w600),
                          //     ),
                          //     SizedBox(width: 2.w),
                          //     Image.asset(
                          //       MyImages.kIndiaFlag,
                          //     ),
                          //   ],
                          // ),
                        ],
                      ),
                      Column(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          Form(
                            key: _formKey,
                            child: TextFormField(
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: thirdColor,
                              ),
                              decoration: InputDecoration(
                                filled: true,
                                fillColor:
                                    mainColor, // Light background for modern look
                                floatingLabelBehavior:
                                    FloatingLabelBehavior.never,
                                contentPadding: EdgeInsets.symmetric(
                                    vertical: 2.h, horizontal: 3.w),
                                hintStyle: TextStyle(
                                    color: thirdColor, fontSize: 12.sp),
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(
                                      12.sp), // Rounded corners
                                  borderSide: BorderSide
                                      .none, // No border, relying on shadow
                                ),
                                enabledBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12.sp),
                                  borderSide: BorderSide(
                                    color:
                                        thirdColor, // Your main color for focus border
                                    width: 2.sp,
                                  ),
                                ),
                                focusedBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12.sp),
                                  borderSide: BorderSide(
                                    color:
                                        thirdColor, // Your main color for focus border
                                    width: 2.sp,
                                  ),
                                ),
                                label: Text(
                                  LocaleKeys.LoginScreen_Enter_your_Phone_Number
                                      .tr(),
                                  style: TextStyle(
                                    color: thirdColor,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                prefixText: '+91 - ',
                                prefixStyle: const TextStyle(
                                    color: thirdColor,
                                    fontWeight: FontWeight.bold),
                              ),
                              keyboardType: TextInputType.number,
                              inputFormatters: <TextInputFormatter>[
                                LengthLimitingTextInputFormatter(10),
                                FilteringTextInputFormatter.digitsOnly
                              ],
                              enabled: (_start == countdown) ? true : false,
                              validator: (value) {
                                if (value!.length != 10) {
                                  return LocaleKeys
                                          .LoginScreen_Enter_10_digit_Phone_Number
                                      .tr();
                                }
                              },
                              onSaved: (value) {
                                _phoneNumber = value;
                              },
                            ),
                          ),
                          SizedBox(height: 2.h),
                          if (_start == countdown)
                            Container(
                              width: double.infinity,
                              child: ElevatedButton(
                                style: ButtonStyle(
                                  backgroundColor:
                                      MaterialStateProperty.all(thirdColor),
                                  textStyle: MaterialStateProperty.all(
                                    const TextStyle(
                                      color: mainContrastColor,
                                    ),
                                  ),
                                ),
                                onPressed: () async {
                                  if (!_formKey.currentState!.validate()) {
                                    return;
                                  }
                                  _formKey.currentState!.save();
                                  {
                                    setState(() {
                                      _isLoading = true;
                                    });
                                    bool result =
                                        await Provider.of<AuthProvider>(context,
                                                listen: false)
                                            .login(int.parse(_phoneNumber!));
                                    setState(() {
                                      _isLoading = false;
                                    });

                                    // if (result) {
                                    //   myAnalytics.setLogEvent(
                                    //       'SendPhoneDetails-Worked',
                                    //       {'phoneNumber': _phoneNumber});
                                    //   Provider.of<AuthProvider>(context,
                                    //           listen: false)
                                    //       .changeOtpStatus(true);
                                    // } else {
                                    //   myAnalytics.setLogEvent(
                                    //       'SendPhoneDetails-NumberAlreadyExist', {
                                    //     'phoneNumber': _phoneNumber,
                                    //     'status': 'Number Already Exist'
                                    //   });
                                    //   ScaffoldMessenger.of(context).showSnackBar(
                                    //     const SnackBar(
                                    //         content: Text(
                                    //             'Phone Number already registered with an account')),
                                    //   );
                                    // }
                                  }
                                },
                                child: Text(
                                  LocaleKeys.LoginScreen_send_otp.tr(),
                                  style: const TextStyle(
                                    color: mainColor,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ),
                          SizedBox(height: 5.h),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
    );
  }
}
