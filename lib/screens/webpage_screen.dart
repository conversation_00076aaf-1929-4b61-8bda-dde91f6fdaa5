import 'dart:io';

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:sizer/sizer.dart';
import 'package:zupid/common_things/my_colors.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../common_things/analytics.dart';

class WebpageScreen extends StatefulWidget {
  static const routeName = '/webpage_screen';

  @override
  State<WebpageScreen> createState() => _WebpageScreenState();
}

class _WebpageScreenState extends State<WebpageScreen> {
  bool _isAnalytics = true;
  late final MyAnalytics myAnalytics;
  bool isLoading = true;
  bool firstTime = true;

  ///

  String _title = 'Webpage';
  WebViewController? controller;

  @override
  Widget build(BuildContext context) {
    //analytics
    if (_isAnalytics) {
      myAnalytics = Provider.of<MyAnalytics>(context, listen: false);
      myAnalytics.setLogEvent('WebpageScreen', {});
      _isAnalytics = false;
    }
    //

    final args = ModalRoute.of(context)!.settings.arguments as List;
    final _title = args[0];
    final _url = args[1];
    //
    if (isLoading) {
      controller = WebViewController()
        ..setJavaScriptMode(JavaScriptMode.unrestricted)
        ..setBackgroundColor(const Color(0x00000000))
        ..setNavigationDelegate(
          NavigationDelegate(
            onProgress: (int progress) {
              // Update loading bar.
            },
            onPageStarted: (String url) {},
            onPageFinished: (String url) {},
            onWebResourceError: (WebResourceError error) {},
            onNavigationRequest: (NavigationRequest request) {
              if (request.url.startsWith('https://www.youtube.com/')) {
                return NavigationDecision.prevent;
              }
              return NavigationDecision.prevent;
            },
          ),
        )
        ..loadRequest(Uri.parse(_url));
      isLoading = false;
    }
    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        title: Text(
          '',
          style: TextStyle(color: Colors.black),
        ),
      ),
      body: Padding(
        padding: EdgeInsets.all(2.5.w),
        child: WebViewWidget(controller: controller!),
      ),
    );
  }
}
