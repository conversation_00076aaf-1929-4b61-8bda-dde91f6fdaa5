import 'package:flutter/material.dart';
import 'package:zupid/common_things/my_colors.dart';
import 'package:zupid/providers/database_provider.dart';
import 'package:zupid/widgets/loading_widget.dart';
import 'package:provider/provider.dart';

class ChooseFromListScreen extends StatefulWidget {
  static const routeName = '/choose-from-list-screen';
  @override
  State<ChooseFromListScreen> createState() => _ChooseFromListScreenState();
}

class _ChooseFromListScreenState extends State<ChooseFromListScreen> {
  bool _isLoading = false;
  bool firstRun = true;
  String title = ''; // Screen title
  Map<dynamic, dynamic> options = {}; // Map of options (name, ID)
  int selectedOptionId = 0; // Current selected option ID
  String payloadKeyName = '';
  String routeName = '';

  Future<bool> _onWillPop() async {
    // Save the selected option before popping the screen
    // print('Selected option ID: $selectedOptionId');
    return true; // Allow the screen to be popped
  }

  @override
  Widget build(BuildContext context) {
    if (firstRun) {
      final args = ModalRoute.of(context)!.settings.arguments as List;
      title = args[0];
      options = args[1];
      selectedOptionId = args[2];
      payloadKeyName = args[3];
      routeName = args[4];
      firstRun = false;
    }
    return WillPopScope(
      onWillPop: (() async {
        setState(() {
          _isLoading = true;
        });
        //
        Map<String, String> _payloadData = {
          payloadKeyName: selectedOptionId.toString(),
        };
        await Provider.of<DatabaseProvider>(context, listen: false)
            .updateUserInfo(routeName, _payloadData);
        //
        setState(() {
          _isLoading = false;
        });
        return true;
      }),
      child: _isLoading
          ? LoadingWidget()
          : Scaffold(
              appBar: AppBar(
                title: Text(title), // Dynamic title
              ),
              body: ListView.builder(
                itemCount: options.length,
                itemBuilder: (context, index) {
                  // Convert the map into a list for easier access
                  final String optionName = options.keys.elementAt(index);
                  final int optionId = options.values.elementAt(index);
                  return ListTile(
                    title: Text(
                      optionName,
                      style: TextStyle(
                          color: selectedOptionId == optionId
                              ? thirdColor
                              : mainContrastColor),
                    ),
                    trailing: selectedOptionId == optionId
                        ? Icon(Icons.check, color: thirdColor)
                        : null,
                    onTap: () {
                      setState(() {
                        selectedOptionId =
                            optionId; // Update selected option ID
                      });
                    },
                  );
                },
              ),
            ),
    );
  }
}
