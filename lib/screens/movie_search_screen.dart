import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import 'package:zupid/common_things/images.dart';
import 'package:zupid/common_things/my_colors.dart';
import 'package:zupid/common_things/my_fucntions.dart';
import 'package:zupid/providers/auth_provider.dart';
import 'package:zupid/providers/database_provider.dart';
import 'package:provider/provider.dart';

class MovieSearchScreen extends StatefulWidget {
  static const routeName = '/movie-search-screen';

  @override
  _MovieSearchScreenState createState() => _MovieSearchScreenState();
}

class _MovieSearchScreenState extends State<MovieSearchScreen> {
  // TODO
  // IMP: MAXIMUM LIMIT OF 15 CAN BE BREACHED IF USER SELECT MANY MOVIES IN SINGLE SEARCH
  // LEABING IT FOR NOW WILL CORRECT IT LATER
  //
  //
  FocusNode _focusNode = FocusNode();

  @override
  void dispose() {
    _focusNode.dispose();
    super.dispose();
  }

  //
  TextEditingController _controller = TextEditingController();
  List movies = [];
  bool isLoading = false;
  Set<String> addedMovies = {}; // To keep track of added movies

  Future<void> searchMovies(String query) async {
    setState(() {
      isLoading = true;
    });

    movies = await Provider.of<DatabaseProvider>(context, listen: false)
        .searchMovie(query);

    setState(() {
      isLoading = false;
    });
  }

  Future<void> addToInterests(String movieTitle, String imdbId) async {
    setState(() {
      addedMovies.add(imdbId); // Add the IMDb ID to the set
    });
    callSnackfold(context, 'Adding $movieTitle to your interests!');
    // ScaffoldMessenger.of(context).showSnackBar(
    //   SnackBar(content: Text('Adding $movieTitle to your interests!')),
    // );
    Provider.of<DatabaseProvider>(context, listen: false)
        .UserSelectMovie(imdbId)
        .then((value) => callSnackfold(context, '$movieTitle is added!'));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        // title: Text('Add your favourite movies/tv shows'),
        // // backgroundColor: Col, // Modern color for AppBar
        elevation: 0,
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [mainColor, mainContrastColor.withOpacity(0.5)],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
        ),
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            TextField(
              controller: _controller,
              focusNode: _focusNode, // Add focus node here
              decoration: InputDecoration(
                hintText: 'Search for a movie or tv shows...',
                hintStyle: TextStyle(color: mainContrastColor.withOpacity(0.5)),
                prefixIcon: Icon(Icons.search, color: mainContrastColor),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(30),
                  borderSide: BorderSide.none,
                ),
                filled: true,
                fillColor: mainContrastColor.withOpacity(0.3),
              ),
              onSubmitted: (value) {
                if (value.isNotEmpty) {
                  searchMovies(value);
                }
              },
            ),
            SizedBox(height: 2.h),
            ElevatedButton(
              onPressed: () {
                // Close the keyboard
                FocusScope.of(context).unfocus();
                if (_controller.text.isNotEmpty) {
                  searchMovies(_controller.text);
                }
              },
              child: Text(
                'Search',
                style: TextStyle(color: mainContrastColor),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: mainContrastColor.withOpacity(0.5),
                padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.w),

                // primary: Colors.deepPurple,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(30),
                ),
                elevation: 4,
              ),
            ),
            SizedBox(height: 3.h),
            Expanded(
              child: isLoading
                  ? const Center(
                      child: CircularProgressIndicator(
                      color: mainContrastColor,
                    ))
                  : movies.isNotEmpty
                      ? ListView.builder(
                          itemCount: movies.length,
                          itemBuilder: (context, index) {
                            final movie = movies[index];
                            final imdbId = movie['imdbID'];
                            final isAdded = addedMovies.contains(imdbId);
                            return MovieCard(
                              title: movie['Title'],
                              year: movie['Year'],
                              posterUrl: movie['Poster'],
                              onAddToInterests: isAdded
                                  ? null
                                  : () =>
                                      addToInterests(movie['Title'], imdbId),
                              buttonText: isAdded ? 'Added!' : '+ Add',
                            );
                          },
                        )
                      : Center(
                          child: Text(
                          'No movies/tv shows found',
                          style: TextStyle(
                              color: mainContrastColor,
                              fontWeight: FontWeight.bold,
                              fontSize: 14.sp),
                        )),
            ),
          ],
        ),
      ),
    );
  }
}

class MovieCard extends StatelessWidget {
  final String title;
  final String year;
  final String posterUrl;
  final VoidCallback? onAddToInterests;
  final String buttonText;

  const MovieCard({
    required this.title,
    required this.year,
    required this.posterUrl,
    this.onAddToInterests,
    required this.buttonText,
  });

  @override
  Widget build(BuildContext context) {
    final myUserInfo =
        Provider.of<DatabaseProvider>(context, listen: true).getMyUserInfo;
    int maximumAllowed = Provider.of<AuthProvider>(context, listen: false)
        .getInterestLimit['movies'];
    int currentMoviesLength = myUserInfo.allInterests.movies.length;
    return Card(
      margin: EdgeInsets.symmetric(vertical: 10),
      elevation: 4, // Increased elevation for a modern look
      color: Colors.transparent,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ClipRRect(
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(15),
              bottomLeft: Radius.circular(15),
            ),
            child: Image.network(
              posterUrl,
              height: 150,
              width: 100,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  height: 150,
                  width: 100,
                  color: Colors.grey[800],
                  child: Center(
                    child: Image.asset(
                      MyImages.kMovieIcon,
                      height: 50,
                      width: 50,
                      fit: BoxFit.contain,
                    ),
                  ),
                );
              },
            ),
          ),
          SizedBox(width: 2.h),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(12.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.bold,
                      color: mainContrastColor, // Modern text color
                    ),
                  ),
                  SizedBox(height: 1.h),
                  Text(
                    'Year: $year',
                    style: TextStyle(
                        fontSize: 12.sp,
                        color: Colors.grey[600],
                        fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 1.h),
                  ElevatedButton(
                    onPressed: currentMoviesLength >= maximumAllowed
                        ? () {
                            callSnackfold(context,
                                'Maximum of $maximumAllowed movies allowed');
                          }
                        : onAddToInterests,
                    style: ElevatedButton.styleFrom(
                      disabledBackgroundColor:
                          mainContrastColor.withOpacity(0.5),
                      disabledForegroundColor: mainContrastColor,
                      backgroundColor: mainContrastColor.withOpacity(0.5),
                      padding:
                          EdgeInsets.symmetric(horizontal: 2.w, vertical: 1.w),
                      // primary: Colors.deepPurple, // Primary color for button
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(1.w),
                      ),
                    ),
                    child: Text(
                      buttonText,
                      style:
                          TextStyle(color: mainContrastColor, fontSize: 12.sp),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
