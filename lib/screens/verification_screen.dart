import 'dart:io';

import 'package:camera/camera.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:zupid/translations/locale_keys.g.dart';
import 'package:provider/provider.dart';
import 'package:sizer/sizer.dart';
import 'package:zupid/common_things/my_colors.dart';
import 'package:zupid/common_things/my_fucntions.dart';
import 'package:zupid/providers/database_provider.dart';
import 'package:zupid/widgets/loading_widget.dart';

import '../common_things/analytics.dart';

class VerificationScreen extends StatefulWidget {
  static const routeName = '/verification-screen';

  VerificationScreen();

  @override
  _VerificationScreenState createState() => _VerificationScreenState();
}

class _VerificationScreenState extends State<VerificationScreen> {
  /////
  bool _isAnalytics = true;
  late final MyAnalytics myAnalytics;
  //////
  bool cameraPermission = true;
  bool _isLoading = false;
  CameraController? _controller;
  Future<void>? _initializeControllerFuture;
  CameraDescription? camera;

  @override
  void initState() {
    super.initState();
    // To display the current output from the Camera,
    // create a CameraController.
  }

  @override
  void dispose() {
    _controller!.dispose();
    super.dispose();
  }

  Future<void> _takePhoto() async {
    try {
      await _initializeControllerFuture;
      // Take the picture and get a file

      final image = await _controller!.takePicture();

      File file = File(image.path);
      setState(() {
        _isLoading = true;
      });
      final status = await Provider.of<DatabaseProvider>(context, listen: false)
          .verifyImage(file);
      setState(() {
        _isLoading = true;
      });

      if (status) {
        Navigator.of(context).pop();
      }
    } catch (e) {}
  }

  @override
  Widget build(BuildContext context) {
    //////////
    if (_isAnalytics) {
      myAnalytics = Provider.of<MyAnalytics>(context, listen: false);
      myAnalytics.setLogEvent('VerificationScreen', {});
      _isAnalytics = false;
    }
    ////////////
    final args = ModalRoute.of(context)!.settings.arguments as List;
    camera = args[0];
    //
    _controller = CameraController(
      // Get a specific camera from the list of available cameras.
      // Define the resolution to use.
      camera!,
      ResolutionPreset.medium,
    );
    // Next, initialize the controller. This returns a Future.
    _initializeControllerFuture = _controller!.initialize();
    ////////
    int verifyProfileStatus =
        Provider.of<DatabaseProvider>(context, listen: false).getVerifyProfile;
    //
    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        title: Text(
          LocaleKeys.verification_screen_Verify_Profile.tr(),
          style: TextStyle(
              // color: Colors.grey,
              // fontWeight: FontWeight.bold,
              ),
        ),
      ),
      body: _isLoading
          ? LoadingWidget()
          : Padding(
              padding: EdgeInsets.all(5.w),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  SizedBox(height: 5.h),
                  if (verifyProfileStatus == -1 || verifyProfileStatus == -2)
                    Text(
                      '${LocaleKeys.verification_screen_txt_1.tr()}\n\n${LocaleKeys.verification_screen_txt_2.tr()}',
                      style:
                          TextStyle(color: mainContrastColor, fontSize: 12.sp),
                    ),
                  if (verifyProfileStatus == 0)
                    Text(
                      '${LocaleKeys.verification_screen_txt_3.tr()}\n\n${LocaleKeys.verification_screen_txt_4.tr()}',
                      style:
                          TextStyle(color: mainContrastColor, fontSize: 12.sp),
                    ),
                  SizedBox(height: 5.h),
                  if (verifyProfileStatus == -1 || verifyProfileStatus == -2)
                    FutureBuilder<void>(
                      future: _initializeControllerFuture,
                      builder: (context, snapshot) {
                        if (snapshot.connectionState == ConnectionState.done) {
                          cameraPermission = true;
                          return Container(
                              width: double.infinity,
                              height: 50.h,
                              child: CameraPreview(_controller!));
                        } else {
                          return LoadingWidget();
                        }
                      },
                    ),
                  if (verifyProfileStatus == -1 || verifyProfileStatus == -2)
                    SizedBox(height: 5.h),
                  if ((verifyProfileStatus == -1 ||
                          verifyProfileStatus == -2) &&
                      cameraPermission)
                    ElevatedButton.icon(
                      style: ButtonStyle(
                        backgroundColor: MaterialStateProperty.all(thirdColor),
                        textStyle: MaterialStateProperty.all(
                          const TextStyle(color: mainColor),
                        ),
                      ),
                      onPressed: () async {
                        await _takePhoto();
                      },
                      icon: const Icon(
                        color: mainColor,
                        Icons.camera_alt_rounded,
                      ),
                      label: Text(
                        LocaleKeys.Common_Submit.tr(),
                        style: const TextStyle(color: mainColor),
                      ),
                    ),
                  // if (cameraPermission == false)
                  //   Text(
                  //     'Please enable camera permission from setting',
                  //     style: TextStyle(
                  //       color: Colors.grey,
                  //       fontSize: 10.sp,
                  //       fontWeight: FontWeight.bold,
                  //     ),
                  //   )
                ],
              ),
            ),
    );
  }
}
