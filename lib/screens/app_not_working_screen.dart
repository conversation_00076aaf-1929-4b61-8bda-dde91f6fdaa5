import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:sizer/sizer.dart';
import 'package:zupid/providers/database_provider.dart';

import '../providers/auth_provider.dart';

class AppNotWorkingScreen extends StatelessWidget {
  static const routeName = '/app-not-working';
  @override
  Widget build(BuildContext context) {
    final reason =
        Provider.of<AuthProvider>(context, listen: false).getNotWorkingReason;
    return Scaffold(
      body: Padding(
        padding: EdgeInsets.all(10.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: <Widget>[
            Icon(
              Icons.build,
              size: 5.h,
              color: Colors.red,
            ),
            SizedBox(height: 4.h),
            Text(
              reason,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14.sp,
                fontWeight: FontWeight.bold,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
