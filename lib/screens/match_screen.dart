import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:zupid/common_things/my_fucntions.dart';
import 'package:zupid/translations/locale_keys.g.dart';
import 'package:provider/provider.dart';
import 'package:sizer/sizer.dart';
import 'package:zupid/common_things/my_colors.dart';
import 'package:zupid/models/myChatDetails.dart';
import 'package:zupid/models/myUserInfo.dart';
import 'package:zupid/providers/database_provider.dart';

import 'chat_details_screen.dart';

class MatchScreen extends StatelessWidget {
  static const routeName = '/match-screen';

  @override
  Widget build(BuildContext context) {
    //
    final args = ModalRoute.of(context)!.settings.arguments as List;
    MyUserInfo varUserInfo = args[0];
    MyChatDetails varChatDetails = args[1];
    //
    // MyUserInfo varUserInfo =
    //     Provider.of<DatabaseProvider>(context, listen: false).getMyUserInfo;
    List<String> _photos = Provider.of<DatabaseProvider>(context, listen: true)
        .getUserSignedUrlMap[varUserInfo.userId]!;
    // print('hello');
    // print(_photos);
    //
    final tempImageDirectory =
        Provider.of<DatabaseProvider>(context, listen: false)
            .tempDirectoryForImage;
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              mainColor,
              // mainColor,

              mainContrastColor.withOpacity(0.2),
            ],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
        ),
        width: 100.w,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircleAvatar(
                backgroundColor: mainContrastColor,
                radius: 20.w,
                backgroundImage:
                    FileImage(File(tempImageDirectory + _photos[0]))
                // CachedNetworkImageProvider(_photos[0]),
                ),
            SizedBox(height: 8.h),
            Text(
              LocaleKeys.Match_screen_Congrats.tr(),
              style: TextStyle(
                color: mainContrastColor,
                fontSize: 20.sp,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 3.h),
            Text(
              LocaleKeys.Match_screen_Youve_matched_with.tr(),
              style: TextStyle(
                color: mainContrastColor,
                fontSize: 15.sp,
              ),
            ),
            SizedBox(height: 1.h),
            Text(
              varUserInfo.isHideName
                  ? showOnlyFirstLetter(varUserInfo.firstName)
                  : capitalizeFirstLetter(varUserInfo.firstName),
              style: TextStyle(
                color: thirdColor,
                fontSize: 18.sp,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 10.h),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                // go to chat now
                Navigator.of(context)
                    .pushNamed(ChatDetailsScreen.routeName, arguments: [
                  varUserInfo.userId,
                  varChatDetails,
                ]);
                // Add your desired action after a match here
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: mainContrastColor,
                foregroundColor: mainColor,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10.w),
                ),
                padding: EdgeInsets.symmetric(horizontal: 4.h, vertical: 2.h),
              ),
              child: Text(
                LocaleKeys.Match_screen_Send_a_message.tr(),
                style: TextStyle(
                  fontSize: 16.sp,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
