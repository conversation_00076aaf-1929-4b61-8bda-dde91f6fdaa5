import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:zupid/common_things/images.dart';
import 'package:zupid/common_things/my_colors.dart';
import 'package:zupid/translations/locale_keys.g.dart';
import 'package:zupid/widgets/display_user_info.dart';
import 'package:provider/provider.dart';
import 'package:sizer/sizer.dart';

import 'package:zupid/common_things/my_fucntions.dart';
import 'package:zupid/models/myChatDetails.dart';
import 'package:zupid/models/myPromptList.dart';
import 'package:zupid/models/myUserPrefs.dart';
import 'package:zupid/providers/database_provider.dart';
import 'package:zupid/screens/match_screen.dart';
import 'package:zupid/widgets/interests_tab.dart';
import 'package:zupid/widgets/loading_widget.dart';
import 'package:zupid/widgets/personality_tab.dart';

import '../common_things/analytics.dart';
import '../models/myUserInfo.dart';
import '../models/myUserInfo2.dart';
import '../widgets/my_stack_button.dart';

class SwipeScreen extends StatefulWidget {
  static const routeName = '/swipe-screen';
  @override
  State<SwipeScreen> createState() => _SwipeScreenState();
}

class _SwipeScreenState extends State<SwipeScreen>
    with SingleTickerProviderStateMixin {
  // extra code for zupid//
  // Tab controller
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(
        length: 3, vsync: this); // 3 tabs: Looks, Personality, Interest
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  //
  //
  bool _isAnalytics = true;
  late final MyAnalytics myAnalytics;
  bool dataLoadedMapCondition = false;
  //////
  final ScrollController _scrollController = ScrollController();
  //
  bool _isLoading = false;
  MyUserInfo? varUserInfo;
  int age = 0;
  int distance = 0;
  //
  List<String> _photos = [];
  //////////
  // final double _appBarHeight = kToolbarHeight;
  // final double _bottomNavHeight = kBottomNavigationBarHeight;
  // final double _topBarHeight =
  ///
  //// these things added later to allow for _dataloaded //
  bool? noMoreSwipeProfilesLeft;
  List<MyUserInfo>? swipeProfiles;
  int? totalProfiles;
  int? currentIndex;
  MyUserInfo? _myUserInfo;
  MyUserInfo2? _myUserInfo2;
  MyUserPrefs? _myUserPrefs;
  Map<int, int>? _userInfo2Params;
  List<MyPrompt>? _promptList;
  //
  // Function to show the message modal dialog
  void _showMessageDialog(BuildContext context) {
    TextEditingController _messageController = TextEditingController();

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent, // Make the background transparent
      builder: (BuildContext context) {
        return Container(
          decoration: BoxDecoration(
            color: mainColor,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(5.w),
              topRight: Radius.circular(5.w),
            ),
            boxShadow: [
              BoxShadow(
                color: mainContrastColor.withOpacity(0.2),
                blurRadius: 2.w,
                spreadRadius: 1.w,
              ),
            ],
          ),
          padding: EdgeInsets.only(
            bottom: MediaQuery.of(context).viewInsets.bottom,
            left: 5.w,
            right: 5.w,
            top: 5.h,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(height: 2.h),
              // Text Field with Modern Design
              Container(
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(1.h),
                  border: Border.all(color: Colors.grey[300]!),
                ),
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 2.h),
                  child: TextField(
                    inputFormatters: <TextInputFormatter>[
                      LengthLimitingTextInputFormatter(100),
                    ],
                    controller: _messageController,
                    decoration: InputDecoration(
                      hintText: 'Leave a message :)',
                      border: InputBorder.none,
                      hintStyle: TextStyle(color: Colors.grey[500]),
                    ),
                    maxLines: 3,
                    style: TextStyle(color: Colors.black87),
                  ),
                ),
              ),
              SizedBox(height: 2.h),
              // Gradient Send Button
              Container(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () {
                    String message = _messageController.text.trim();
                    if (message.length >= 1) {
                      Navigator.pop(context); // Close the dialog
                      _sendMessage(message); // Send the message
                    } else {
                      callSnackfoldTop(context,
                          'Come on, don’t leave it blank!');
                    }
                  },
                  style: ElevatedButton.styleFrom(
                    // primary: Colors.transparent,
                    shadowColor: Colors.transparent,
                    backgroundColor: rightSwipeButtonColor,
                    padding: EdgeInsets.symmetric(vertical: 1.h),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(1.h),
                    ),
                  ),
                  child: Text(
                    'Send Message',
                    style: TextStyle(
                      fontSize: 12.sp,
                      fontWeight: FontWeight.bold,
                      color: mainColor,
                    ),
                  ),
                ),
              ),
              SizedBox(height: 2.h),
            ],
          ),
        );
      },
    );
  }

  // Function to handle sending the message
  void _sendMessage(String message) async {
    if (varUserInfo == null) {
      callToast("Something went wrong. Please try again.");
      return;
    }

    if (_userInfo2Params?[1] == null || _userInfo2Params![1]! <= 0) {
      callToast(LocaleKeys.SwipeScreen_No_likes_left.tr());
      return;
    }

    if (_scrollController.hasClients) {
      _scrollController.jumpTo(0);
    }
    _tabController.animateTo(0); // Switch to 'Looks' tab

    Provider.of<DatabaseProvider>(context, listen: false).addIndexByOne();
    MyChatDetails? chatDetails = await Provider.of<DatabaseProvider>(context,
            listen: false)
        .addOrUpdateMatching(varUserInfo!, 1, message); // Pass the message here

    if (chatDetails != null) {
      Navigator.of(context).pushNamed(
        MatchScreen.routeName,
        arguments: [varUserInfo, chatDetails],
      );
    }
  }

  //
  @override
  Widget build(BuildContext context) {
    //////////
    if (_isAnalytics) {
      myAnalytics = Provider.of<MyAnalytics>(context, listen: false);
      myAnalytics.setLogEvent('SwipeScreen', {});
      _isAnalytics = false;
    }
    ////////////
    final screenPadding = 1.w;
    double availableHeight = 100.h;
    final double screenWidth = 100.w - 2 * screenPadding;
    final double childWidth =
        screenWidth / 2; // divide by the number of columns
    final eachPhotoHeight = (availableHeight / 3);
    final double childAspectRatio = childWidth / eachPhotoHeight;
    // print(childAspectRatio);
    // print(screenHeight);
    // print(availableHeight);

    ////// data loaded condition
    final _dataLoadedMap =
        Provider.of<DatabaseProvider>(context, listen: true).getDataLoadedMap;
    // dataLoadedMapCondition = _dataLoadedMap['myUserInfo']! &&
    //     _dataLoadedMap['myUserInfo2']! &&
    //     _dataLoadedMap['myUserPrefs']! &&
    //     _dataLoadedMap['swipeScreen']! &&
    //     _dataLoadedMap['similarScreen']!;
    dataLoadedMapCondition =
        _dataLoadedMap['loadall']! && _dataLoadedMap['swipeScreen']!;

    ///
    if (dataLoadedMapCondition) {
      ////////////// fetch swipe profiles and swipe index //
      noMoreSwipeProfilesLeft =
          Provider.of<DatabaseProvider>(context, listen: true)
              .getNoSwipeProfilesLeft;
      swipeProfiles = Provider.of<DatabaseProvider>(context, listen: true)
          .getSwipeScreenProfilesList;
      totalProfiles = swipeProfiles!.length;
      currentIndex = Provider.of<DatabaseProvider>(context, listen: true)
          .getCurrentSwipeScreenProfileIndex;
      _myUserInfo =
          Provider.of<DatabaseProvider>(context, listen: true).getMyUserInfo;
      _myUserInfo2 =
          Provider.of<DatabaseProvider>(context, listen: true).getMyUserInfo2;
      _myUserPrefs =
          Provider.of<DatabaseProvider>(context, listen: true).getMyUserPrefs;
      // userinfo
      _promptList =
          Provider.of<DatabaseProvider>(context, listen: true).getPromptList;
      if (currentIndex! < totalProfiles!) {
        // if that is in simialr profiles then add index by one
        // Provider.of<DatabaseProvider>(context, listen: false)
        //     .checkIfSwipeUserIdInSimilarProfilesList();
        //
        varUserInfo = swipeProfiles![currentIndex!];
        age = findAge(varUserInfo!.dob);
        distance = distanceBetweenGeoLocator(
            _myUserInfo!.latitude,
            _myUserInfo!.longitude,
            varUserInfo!.latitude,
            varUserInfo!.longitude,
            _myUserPrefs!);
        _photos = Provider.of<DatabaseProvider>(context, listen: true)
            .getUserSignedUrlMap[varUserInfo!.userId]!;
        if (_photos.contains('isLoading')) {
          _isLoading = true;
          // print("isLoading");
        } else {
          _isLoading = false;
          // print("Loading done");
        }
      } else {
        // If we're at or beyond the last profile, ensure we don't try to access invalid data
        // This prevents showing stale profile data when transitioning to "no more profiles" screen
        _isLoading = false;
      }
      //
      _userInfo2Params = Provider.of<DatabaseProvider>(context, listen: true)
          .getUserInfo2Params;
      // check if current index userid is in
      // print('about me: ${varUserInfo!.aboutMe}');
    }
    // print('test:${varUserInfo!.userId}');
    // print(_userInfo2Params);

    return dataLoadedMapCondition == false
        ? LoadingWidget()
        : (noMoreSwipeProfilesLeft! &&
                    currentIndex! >= swipeProfiles!.length) ||
                currentIndex! >= totalProfiles!
            ? Padding(
                padding: EdgeInsets.all(10.w),
                child: Center(
                    child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    SizedBox(
                      height: 5.h,
                    ),
                    Container(
                      alignment: Alignment.center,
                      // width: 50.w,
                      height: 10.h,
                      child: Image.asset(
                        MyImages.kRose,
                      ),
                    ),
                    SizedBox(
                      height: 5.h,
                    ),
                    Text(
                      'Thanks for joining early',
                      style: TextStyle(
                          fontSize: 12.sp, fontWeight: FontWeight.bold),
                    ),
                    Text(
                      'You’re part of something real!',
                      style: TextStyle(
                          fontSize: 12.sp, fontWeight: FontWeight.bold),
                    ),
                    SizedBox(
                      height: 3.h,
                    ),
                    Text(
                      "No profiles nearby right now.\nTry adjusting your preferences or check back later.",
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 10.sp,
                      ),
                    ),
                  ],
                )),
              )
            // : _isLoading
            //     ? LoadingWidget()
            : varUserInfo == null
                ? LoadingWidget()
                : Stack(
                    children: [
                      Container(
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              mainColor,
                              // mainColor,

                              mainContrastColor.withOpacity(0.1),
                            ],
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                          ),
                        ),
                      ),
                      // Modern and styled tab bar
                      Padding(
                        padding: EdgeInsetsDirectional.fromSTEB(0, 8.h, 0, 0),
                        child: Container(
                          // height: 300, // Adjust height as per your content
                          child: TabBarView(
                            controller: _tabController,
                            children: [
                              SingleChildScrollView(
                                controller: _scrollController,
                                child: AnimatedSwitcher(
                                  duration: const Duration(
                                      milliseconds: 300), // Smoother animation
                                  transitionBuilder: (child, animation) {
                                    return FadeTransition(
                                      opacity: animation,
                                      child: ScaleTransition(
                                        scale: animation,
                                        child: child,
                                      ),
                                    );
                                  },
                                  child: Padding(
                                    key: ValueKey<int>(
                                        currentIndex!), // Unique key
                                    padding: EdgeInsets.all(4
                                        .w), // Increased padding for better spacing
                                    child: CompleteUserInfoDisplay(
                                      photoLink: _photos,
                                      varUserInfo: varUserInfo!,
                                      age: age,
                                      distance: distance,
                                      column2Show: true,
                                      resizeFactor: 1,
                                      promptList: _promptList!,
                                    ),
                                  ),
                                ),
                              ),
                              InterestTab(
                                myUserInfo: varUserInfo!,
                                isEditActive: false,
                              ),
                              PersonalityTab(
                                userInfo: varUserInfo!,
                                isEditActive: false,
                              ),
                            ],
                          ),
                        ),
                      ),
                      Positioned(
                        bottom: 3.h, // Lift buttons a bit higher
                        left: 0,
                        right: 0,
                        child: Padding(
                          padding: EdgeInsets.symmetric(
                              horizontal: 6.w), // Wider spacing
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceAround,
                            children: [
                              GestureDetector(
                                onTap: () async {
                                  if (varUserInfo == null) return;

                                  if (_scrollController.hasClients) {
                                    _scrollController.jumpTo(0);
                                  }
                                  _tabController
                                      .animateTo(0); // Switch to 'Looks'

                                  Provider.of<DatabaseProvider>(context,
                                          listen: false)
                                      .addIndexByOne();
                                  await Provider.of<DatabaseProvider>(context,
                                          listen: false)
                                      .addOrUpdateMatching(
                                          varUserInfo!, -1, "");
                                },
                                child: const LeftSwipeButton(),
                              ),

                              GestureDetector(
                                onTap: () async {
                                  _showMessageDialog(
                                      context); // Show the message dialog
                                },
                                child: const RightSwipeButton(),
                              ),
                              // Tabs at the bottom
                            ],
                          ),
                        ),
                      ),

                      Positioned(
                        top: 0,
                        left: 0,
                        right: 0,
                        child: Align(
                          alignment: Alignment.topCenter,
                          child: Padding(
                            padding: EdgeInsets.fromLTRB(0, 1.h, 0, 2.h),
                            child: Container(
                              width: 80.w,
                              height: 5.h,
                              decoration: BoxDecoration(
                                gradient: const LinearGradient(
                                    begin: Alignment.topCenter,
                                    end: Alignment.bottomCenter,
                                    colors: [mainContrastColor, thirdColor]),
                                borderRadius: BorderRadius.circular(
                                    5.w), // Adjust for capsule shape
                              ),
                              child: Padding(
                                padding: EdgeInsets.symmetric(
                                    vertical: 2.w, horizontal: 1.w),
                                child: TabBar(
                                  dividerColor: Colors.transparent,
                                  controller: _tabController,
                                  unselectedLabelColor:
                                      mainColor, // Set text color for unselected tabs
                                  labelColor: Colors
                                      .white, // Set text color for selected tab
                                  indicatorColor: Colors
                                      .transparent, // Hide default indicator
                                  // indicatorSize: TabBarIndicatorSize.tab,
                                  tabs: [
                                    Tab(
                                        child: Center(
                                            child: Text("Looks",
                                                style: TextStyle(
                                                    fontSize: 9.sp)))),
                                    Tab(
                                        child: Center(
                                            child: Text("Interest",
                                                style: TextStyle(
                                                    fontSize: 9.sp)))),
                                    Tab(
                                        child: Center(
                                            child: Text("Personality",
                                                style: TextStyle(
                                                    fontSize: 9.sp)))),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                      )
                    ],
                  );
  }
}

class MovieCard extends StatelessWidget {
  final String imageUrl;
  final String movieName;
  final String movieYear;

  const MovieCard({
    Key? key,
    required this.imageUrl,
    required this.movieName,
    required this.movieYear,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final _imageUrl =
        "https://m.media-amazon.com/images/M/MV5BZGM5NjliODgtODVlOS00OWZmLWIzYzMtMTI2OWIzMTM1ZGRhXkEyXkFqcGdeQXVyNDUzOTQ5MjY@._V1_SX300.jpg";
    return Padding(
      padding: const EdgeInsets.all(4.0),
      child: Container(
        height: 40.w,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(15),
          boxShadow: const [
            BoxShadow(
              color: mainContrastColor,
              blurRadius: 2,
              offset: Offset(1, 2),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          // crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ClipRRect(
              borderRadius: BorderRadius.vertical(top: Radius.circular(15)),
              child: Image.network(
                _imageUrl,
                height: 30.w,
                width: double.infinity,
                fit: BoxFit.cover,
              ),
            ),
            Padding(
              padding: EdgeInsets.symmetric(vertical: 0, horizontal: 2.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Text(
                    movieName,
                    style: TextStyle(
                      fontWeight: FontWeight.w700,
                      fontSize: 10.sp,
                      color: greyColor,
                    ),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 2,
                  ),
                  SizedBox(height: .5.w),
                  Text(
                    movieYear,
                    style: TextStyle(
                      fontSize: 9.sp,
                      color: greyColor,
                    ),
                  ),
                  SizedBox(
                    height: 1.w,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
