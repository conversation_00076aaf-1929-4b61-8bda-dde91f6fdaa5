import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:sizer/sizer.dart';
import 'package:zupid/common_things/my_colors.dart';
import 'package:zupid/common_things/my_constants.dart';
import 'package:zupid/common_things/my_fucntions.dart';
import 'package:zupid/providers/database_provider.dart';
import 'package:provider/provider.dart';
import 'package:zupid/translations/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';

class AboutMyselfScreen extends StatelessWidget {
  static const routeName = '/about_myself_screen';

  @override
  Widget build(BuildContext context) {
    final _myUserInfo =
        Provider.of<DatabaseProvider>(context, listen: true).getMyUserInfo;

    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.transparent,
        foregroundColor: mainContrastColor,
        systemOverlayStyle: SystemUiOverlayStyle(
          statusBarColor: mainColor,
          statusBarIconBrightness: Brightness.light,
        ),
      ),
      backgroundColor: mainColor,
      body: SingleChildScrollView(
        child: Container(
          height: 100.h,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                mainColor,
                mainContrastColor.withOpacity(0.1),
              ],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Tell us about yourself',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: mainContrastColor,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Share something interesting that makes you unique',
                  style: TextStyle(
                    fontSize: 14,
                    color: mainContrastColor.withOpacity(0.8),
                  ),
                ),
                const SizedBox(height: 32),
                _buildAboutMyselfInput(context, _myUserInfo!.aboutMe),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAboutMyselfInput(BuildContext context, String aboutMe) {
    final _formKey = GlobalKey<FormState>();
    final _aboutMeController = TextEditingController(text: aboutMe);

    return Form(
      key: _formKey,
      child: Column(
        children: [
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 10,
                  offset: Offset(0, 4),
                ),
              ],
            ),
            child: TextFormField(
              controller: _aboutMeController,
              maxLines: 6,
              style: TextStyle(color: mainContrastColor),
              decoration: InputDecoration(
                labelText: 'About Myself',
                labelStyle: TextStyle(
                  color: mainContrastColor.withOpacity(0.8),
                  fontWeight: FontWeight.w500,
                ),
                filled: true,
                fillColor: mainContrastColor.withOpacity(0.1),
                border: OutlineInputBorder(
                  borderSide: BorderSide.none,
                  borderRadius: BorderRadius.circular(16),
                ),
                focusedBorder: OutlineInputBorder(
                  borderSide: BorderSide(color: thirdColor, width: 2),
                  borderRadius: BorderRadius.circular(16),
                ),
                contentPadding: EdgeInsets.all(20),
                errorMaxLines: 2,
                errorStyle: TextStyle(color: thirdColor),
              ),
              textInputAction: TextInputAction.next,
              inputFormatters: <TextInputFormatter>[
                LengthLimitingTextInputFormatter(256),
              ],
              validator: (value) {
                if (value == null || containsOnlySpaces(value)) {
                  return LocaleKeys.EditInfoScreen_Please_enter_your_life_story
                      .tr();
                }
                return null;
              },
            ),
          ),
          const SizedBox(height: 24),
          Padding(
            padding: EdgeInsets.fromLTRB(10.w, 0, 10.w, 0),
            child: SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: thirdColor,
                  padding: EdgeInsets.symmetric(vertical: 10.sp),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: 3,
                  shadowColor: Colors.black.withOpacity(0.2),
                ),
                onPressed: () async {
                  if (_formKey.currentState!.validate()) {
                    _formKey.currentState!.save();
                    final aboutMe = _aboutMeController.text;

                    Provider.of<DatabaseProvider>(context, listen: false)
                        .updateUserInfo(
                      userInfoAboutMeRoute,
                      {'about_me': aboutMe},
                    );
                  }
                },
                child: Text(
                  'Next',
                  style: TextStyle(
                    color: mainColor,
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
