import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/services.dart';
import 'package:zupid/common_things/my_constants.dart';
import 'package:zupid/common_things/my_fucntions.dart';
import 'package:zupid/models/myInterest.dart';
import 'package:zupid/providers/auth_provider.dart';
import 'package:zupid/translations/locale_keys.g.dart';
import 'package:provider/provider.dart';
import 'package:sizer/sizer.dart';
import 'package:zupid/common_things/my_colors.dart';
import 'package:zupid/providers/database_provider.dart';
import 'package:zupid/widgets/loading_widget.dart';
import '../common_things/analytics.dart';

class SelectCategoryInterestScreen extends StatefulWidget {
  final String categoryName;
  static const routeName = '/select-category-interest-screen';

  const SelectCategoryInterestScreen({super.key, required this.categoryName});
  @override
  State<SelectCategoryInterestScreen> createState() =>
      _SelectCategoryInterestScreenState();
}

class _SelectCategoryInterestScreenState
    extends State<SelectCategoryInterestScreen> {
  bool _isAnalytics = true;
  late final MyAnalytics myAnalytics;
  String selectedCategory = '';
  bool _isLoading = false;
  late List<MyInterest> _allInterests;
  List<String> _selectedInterests = [];
  @override
  void initState() {
    super.initState();
    if (_isAnalytics) {
      myAnalytics = Provider.of<MyAnalytics>(context, listen: false);
      myAnalytics.setLogEvent('ChooseInterestScreen', {});
      _isAnalytics = false;
    }
  }

  void _fetchInterests() {
    final databaseProvider =
        Provider.of<DatabaseProvider>(context, listen: false);
    _allInterests = databaseProvider.getInterestList;
    _selectedInterests = databaseProvider.getMyUserInfo.allInterests.interests;
  }

  void _proceedToNextStep() {
    int selectedCount = _selectedInterests
        .where((id) =>
            _allInterests
                .firstWhere((interest) => interest.id == id)
                .category ==
            selectedCategory)
        .length;

    if (selectedCount < 1) {
      callSnackfoldTop(context,
          'Please select at least one interest in "$selectedCategory"');
      return;
    }

    Provider.of<DatabaseProvider>(context, listen: false)
        .EditInterests(_selectedInterests);
    widget.categoryName == "" ? Navigator.of(context).pop() : null;
  }

  @override
  Widget build(BuildContext context) {
    if (widget.categoryName == '') {
      selectedCategory = ModalRoute.of(context)!.settings.arguments as String;
    } else {
      selectedCategory = widget.categoryName;
    }
    _fetchInterests();

    final myUserInfo =
        Provider.of<DatabaseProvider>(context, listen: true).getMyUserInfo;
    int maximumAllowed = Provider.of<AuthProvider>(context, listen: false)
        .getInterestLimit[selectedCategory];
    return Scaffold(
      backgroundColor: mainColor,
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.transparent,
        automaticallyImplyLeading: false,
        centerTitle: true,
        title: Text(
          selectedCategory,
          style: TextStyle(
            fontSize: 16.sp,
            color: mainContrastColor,
            fontWeight: FontWeight.w600,
          ),
        ),
        systemOverlayStyle: SystemUiOverlayStyle(
          statusBarColor: mainColor,
          statusBarIconBrightness: Brightness.light,
        ),
      ),
      body: _isLoading
          ? LoadingWidget()
          : Container(
              height: 100.h,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    mainColor,
                    mainContrastColor.withOpacity(0.1),
                  ],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                ),
              ),
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 4.w),
                child: Column(
                  children: [
                    SizedBox(height: 2.h),
                    Text(
                      'Select up to $maximumAllowed $selectedCategory',
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: mainContrastColor.withOpacity(0.8),
                      ),
                    ),
                    SizedBox(height: 2.h),
                    Expanded(
                      child: Scrollbar(
                        thumbVisibility: true,
                        child: GridView.builder(
                          itemCount: _allInterests
                              .where((interest) =>
                                  interest.category == selectedCategory)
                              .length,
                          gridDelegate:
                              SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 3,
                            childAspectRatio: 0.75,
                            mainAxisSpacing: 6.w,
                            crossAxisSpacing: 3.w,
                          ),
                          itemBuilder: (context, index) {
                            final interest = _allInterests
                                .where((interest) =>
                                    interest.category == selectedCategory)
                                .toList()[index];
                            return InkWell(
                              borderRadius: BorderRadius.circular(2.h),
                              onTap: () {
                                setState(() {
                                  if (_selectedInterests
                                      .contains(interest.id)) {
                                    _selectedInterests.remove(interest.id);
                                  } else if (_selectedInterests
                                          .where((id) =>
                                              _allInterests
                                                  .firstWhere((interest) =>
                                                      interest.id == id)
                                                  .category ==
                                              selectedCategory)
                                          .length <
                                      maximumAllowed) {
                                    _selectedInterests.add(interest.id);
                                  } else {
                                    callSnackfoldTop(context,
                                        'Maximum $maximumAllowed \'$selectedCategory\' allowed!');
                                  }
                                });
                              },
                              child: Container(
                                decoration: BoxDecoration(
                                  color:
                                      _selectedInterests.contains(interest.id)
                                          ? mainContrastColor.withOpacity(0.2)
                                          : mainColor.withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(2.h),
                                  border: Border.all(
                                    color:
                                        _selectedInterests.contains(interest.id)
                                            ? thirdColor
                                            : Colors.grey.withOpacity(0.3),
                                    width: 1.5,
                                  ),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.black.withOpacity(0.1),
                                      blurRadius: 8,
                                      offset: Offset(0, 2),
                                    ),
                                  ],
                                ),
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Expanded(
                                      flex: 3,
                                      child: Padding(
                                        padding: EdgeInsets.all(1.w),
                                        child: ClipRRect(
                                          borderRadius:
                                              BorderRadius.circular(1.h),
                                          child: CachedNetworkImage(
                                            imageUrl: interestR2Folder +
                                                interest.image,
                                            fit: BoxFit.cover,
                                            placeholder: (context, url) =>
                                                LoadingWidget(),
                                            errorWidget:
                                                (context, url, error) => Icon(
                                                    Icons.error,
                                                    color: Colors.grey,
                                                    size: 8.sp),
                                          ),
                                        ),
                                      ),
                                    ),
                                    Expanded(
                                      flex: 1,
                                      child: Padding(
                                        padding: EdgeInsets.symmetric(
                                            horizontal: 1.w),
                                        child: Text(
                                          interest.name,
                                          textAlign: TextAlign.center,
                                          maxLines: 2,
                                          overflow: TextOverflow.ellipsis,
                                          style: TextStyle(
                                            fontSize: 10.sp,
                                            color: _selectedInterests
                                                    .contains(interest.id)
                                                ? thirdColor
                                                : Colors.grey,
                                            fontWeight: _selectedInterests
                                                    .contains(interest.id)
                                                ? FontWeight.w600
                                                : FontWeight.normal,
                                          ),
                                        ),
                                      ),
                                    ),
                                    SizedBox(
                                      height: .5.h,
                                    )
                                  ],
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.symmetric(vertical: 2.h),
                      child: SizedBox(
                        width: 60.w,
                        child: ElevatedButton(
                          style: ElevatedButton.styleFrom(
                            backgroundColor: thirdColor,
                            padding: EdgeInsets.symmetric(vertical: 1.5.h),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(2.h),
                            ),
                            elevation: 3,
                            shadowColor: Colors.black.withOpacity(0.2),
                          ),
                          onPressed: _proceedToNextStep,
                          child: Text(
                            widget.categoryName != "" ? 'Next' : 'Done',
                            style: TextStyle(
                              fontSize: 12.sp,
                              color: mainColor,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
    );
  }
}
