import 'package:flutter/material.dart';
import 'package:zupid/common_things/my_colors.dart';
import 'package:zupid/widgets/display_user_info.dart';
import 'package:zupid/widgets/interests_tab.dart';
import 'package:zupid/widgets/personality_tab.dart';
import 'package:provider/provider.dart';
import 'package:sizer/sizer.dart';

import '../common_things/analytics.dart';
import '../common_things/my_fucntions.dart';
import '../models/myUserInfo.dart';
import '../providers/database_provider.dart';

class UserProfileScreen extends StatefulWidget {
  static const routeName = '/user-profile-screen';

  @override
  State<UserProfileScreen> createState() => _UserProfileScreenState();
}

class _UserProfileScreenState extends State<UserProfileScreen>
    with SingleTickerProviderStateMixin {
  /////
  bool _isAnalytics = true;
  late final MyAnalytics myAnalytics;
  //////
  late TabController _tabController;
  @override
  void initState() {
    super.initState();
    _tabController = TabController(
        length: 3, vsync: this); // 3 tabs: Looks, Personality, Interest
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  final ScrollController _scrollController = ScrollController();

  //
  @override
  Widget build(BuildContext context) {
    //////////
    if (_isAnalytics) {
      myAnalytics = Provider.of<MyAnalytics>(context, listen: false);
      myAnalytics.setLogEvent('UserProfileScreen', {});
      _isAnalytics = false;
    }
    ////////////
    ////
    final screenPadding = 1.w;
    double availableHeight = 100.h;
    final double screenWidth = 100.w - 2 * screenPadding;
    final double childWidth =
        screenWidth / 2; // divide by the number of columns
    final eachPhotoHeight = (availableHeight / 3);
    final double childAspectRatio = childWidth / eachPhotoHeight;
    // print(childAspectRatio);
    //////////
    final args = ModalRoute.of(context)!.settings.arguments as List;
    String varUserId = args[0];
    bool isMe = args[1];
    //
    final _myUserInfo =
        Provider.of<DatabaseProvider>(context, listen: true).getMyUserInfo;
    final _myUserPrefs =
        Provider.of<DatabaseProvider>(context, listen: true).getMyUserPrefs;
    //
    MyUserInfo varUserInfo = isMe
        ? _myUserInfo
        : Provider.of<DatabaseProvider>(context, listen: true)
            .getUserIdMatchedUserInfoMap[varUserId]!;
    int age = findAge(varUserInfo.dob);
    int distance = distanceBetweenGeoLocator(
        _myUserInfo.latitude,
        _myUserInfo.longitude,
        varUserInfo.latitude,
        varUserInfo.longitude,
        _myUserPrefs);
    List<String> _photos = Provider.of<DatabaseProvider>(context, listen: true)
        .getUserSignedUrlMap[varUserInfo.userId]!;
    final _promptList =
        Provider.of<DatabaseProvider>(context, listen: true).getPromptList;
    final _allPersonalityTraits =
        Provider.of<DatabaseProvider>(context, listen: false)
            .getAllPersonalityTraitsList;

    //
    return Scaffold(
      appBar: AppBar(
        elevation: 0,
      ),
      body: Stack(
        children: [
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  mainColor,
                  // mainColor,
                  mainContrastColor.withOpacity(0.1),
                ],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ),
            ),
          ),

          // Modern and styled tab bar
          Padding(
            padding: EdgeInsetsDirectional.fromSTEB(0, 8.h, 0, 0),
            child: Container(
              // height: 300, // Adjust height as per your content
              child: TabBarView(
                controller: _tabController,
                children: [
                  SingleChildScrollView(
                    controller: _scrollController,
                    child: Padding(
                      // key: ValueKey<int>(currentIndex!), // Unique key
                      padding: EdgeInsets.all(
                          4.w), // Increased padding for better spacing
                      child: CompleteUserInfoDisplay(
                        photoLink: _photos,
                        varUserInfo: varUserInfo,
                        age: age,
                        distance: distance,
                        column2Show: true,
                        resizeFactor: 1,
                        promptList: _promptList,
                      ),
                    ),
                  ),
                  InterestTab(
                    myUserInfo: varUserInfo,
                    isEditActive: false,
                  ),
                  PersonalityTab(
                    userInfo: varUserInfo,
                    // allPersonalityTraits: _allPersonalityTraits,
                    isEditActive: false,
                  ),
                ],
              ),
            ),
          ),
          // Positioned(
          //   bottom: 4.h, // Lift buttons a bit higher
          //   left: 0,
          //   right: 0,
          //   child: Padding(
          //     padding: EdgeInsets.symmetric(
          //         horizontal: 6.w), // Wider spacing
          //     child: Row(
          //       mainAxisAlignment: MainAxisAlignment.spaceAround,
          //       children: [
          //         GestureDetector(
          //           onTap: () async {
          //             // print('tap1');
          //             _scrollController.jumpTo(0);
          //             Provider.of<DatabaseProvider>(context,
          //                     listen: false)
          //                 .addIndexByOne();
          //             MyChatDetails? chatDetails =
          //                 await Provider.of<DatabaseProvider>(context,
          //                         listen: false)
          //                     .addOrUpdateMatching(varUserInfo!, -1);
          //           },
          //           child: LeftSwipeButton(),
          //         ),
          //         GestureDetector(
          //           onTap: () async {
          //             // print('tap2');
          //             _scrollController.jumpTo(0);

          //             if (_userInfo2Params![2]! <= 0) {
          //               callToast(LocaleKeys
          //                   .SwipeScreen_No_super_likes_left.tr());
          //               return;
          //             }
          //             Provider.of<DatabaseProvider>(context,
          //                     listen: false)
          //                 .addIndexByOne();
          //             MyChatDetails? chatDetails =
          //                 await Provider.of<DatabaseProvider>(context,
          //                         listen: false)
          //                     .addOrUpdateMatching(varUserInfo!, 2);
          //             if (chatDetails != null) {
          //               Navigator.of(context).pushNamed(
          //                   MatchScreen.routeName,
          //                   arguments: [varUserInfo, chatDetails]);
          //             }
          //           },
          //           child: const SuperSwipeButton(),
          //         ),
          //         GestureDetector(
          //           onTap: () async {
          //             // print('tap3');
          //             _scrollController.jumpTo(0);

          //             if (_userInfo2Params![1]! <= 0 &&
          //                 _myUserInfo!.isMembership == false) {
          //               callToast(
          //                   LocaleKeys.SwipeScreen_No_likes_left.tr());
          //               return;
          //             }
          //             Provider.of<DatabaseProvider>(context,
          //                     listen: false)
          //                 .addIndexByOne();
          //             MyChatDetails? chatDetails =
          //                 await Provider.of<DatabaseProvider>(context,
          //                         listen: false)
          //                     .addOrUpdateMatching(varUserInfo!, 1);
          //             if (chatDetails != null) {
          //               Navigator.of(context).pushNamed(
          //                   MatchScreen.routeName,
          //                   arguments: [varUserInfo, chatDetails]);
          //             }
          //           },
          //           child: RightSwipeButton(),
          //         ),
          //         // Tabs at the bottom
          //       ],
          //     ),
          //   ),
          // ),
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: Align(
              alignment: Alignment.topCenter,
              child: Padding(
                padding: EdgeInsets.fromLTRB(0, 1.h, 0, 2.h),
                child: Container(
                  width: 80.w,
                  height: 5.h,
                  decoration: BoxDecoration(
                    gradient: const LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [mainContrastColor, thirdColor]),
                    borderRadius:
                        BorderRadius.circular(5.w), // Adjust for capsule shape
                  ),
                  child: Padding(
                    padding:
                        EdgeInsets.symmetric(vertical: 2.w, horizontal: 1.w),
                    child: TabBar(
                      dividerColor: Colors.transparent,
                      controller: _tabController,
                      unselectedLabelColor:
                          mainColor, // Set text color for unselected tabs
                      labelColor:
                          Colors.white, // Set text color for selected tab
                      indicatorColor:
                          Colors.transparent, // Hide default indicator
                      // indicatorSize: TabBarIndicatorSize.tab,
                      tabs: [
                        Tab(
                            child: Center(
                                child: Text("Looks",
                                    style: TextStyle(fontSize: 9.sp)))),
                        Tab(
                            child: Center(
                                child: Text("Interest",
                                    style: TextStyle(fontSize: 9.sp)))),
                        Tab(
                            child: Center(
                                child: Text("Personality",
                                    style: TextStyle(fontSize: 9.sp)))),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          )
        ],
      ),
    );
  }
}
