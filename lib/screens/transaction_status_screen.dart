import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/container.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:zupid/translations/locale_keys.g.dart';
import 'package:provider/provider.dart';
import 'package:sizer/sizer.dart';

import '../common_things/analytics.dart';

class TransactionStatusScreen extends StatelessWidget {
  static const routeName = '/transaction-status-screen';
  //
  bool _isAnalytics = true;
  late final MyAnalytics myAnalytics;

  ///

  @override
  Widget build(BuildContext context) {
    //////////
    if (_isAnalytics) {
      myAnalytics = Provider.of<MyAnalytics>(context, listen: false);
      myAnalytics.setLogEvent('TransactionStatusScreen', {});
      _isAnalytics = false;
    }
    ////////////
    final args = ModalRoute.of(context)!.settings.arguments as List;
    final status = args[0];
    //analytics
    if (_isAnalytics) {
      myAnalytics = Provider.of<MyAnalytics>(context, listen: false);
      myAnalytics.setLogEvent('Transaction - $status', {});
      _isAnalytics = false;
    }
    //

    return Scaffold(
      body: Container(
        height: 100.h,
        width: 100.h,
        color: Colors.white,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              SizedBox(height: 10.h),
              if (status == 'Success')
                FaIcon(
                  FontAwesomeIcons.checkCircle,
                  color: Colors.green[700],
                  size: 20.sp,
                ),
              if (status == 'Failed')
                FaIcon(
                  FontAwesomeIcons.exclamationCircle,
                  color: Colors.red[700],
                  size: 20.sp,
                ),
              if (status == 'Cancel')
                FaIcon(
                  FontAwesomeIcons.exclamationCircle,
                  color: Colors.red[700],
                  size: 20.sp,
                ),
              if (status == 'Error')
                FaIcon(
                  FontAwesomeIcons.exclamationTriangle,
                  color: Colors.yellow[700],
                  size: 20.sp,
                ),
              SizedBox(height: 3.h),
              if (status == 'Success')
                Text(
                  LocaleKeys.payu_screen_tx_succes.tr(),
                  style: TextStyle(fontSize: 15.sp),
                ),
              if (status == 'Failed')
                Text(
                  LocaleKeys.payu_screen_tx_failed.tr(),
                  style: TextStyle(fontSize: 15.sp),
                ),
              if (status == 'Cancel')
                Text(
                  LocaleKeys.payu_screen_tx_cancel.tr(),
                  style: TextStyle(fontSize: 15.sp),
                ),
              if (status == 'Error')
                Text(
                  LocaleKeys.payu_screen_error.tr(),
                  style: TextStyle(fontSize: 15.sp),
                ),
              SizedBox(height: 20.h),
              ElevatedButton(
                style: ElevatedButton.styleFrom(foregroundColor: Colors.black),
                onPressed: () {
                  Navigator.of(context).pop();
                },
                child: Text('  ${LocaleKeys.Common_close.tr()}  '),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
