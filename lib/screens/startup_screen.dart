import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:zupid/common_things/images.dart';
import 'package:zupid/common_things/my_colors.dart';
import 'package:provider/provider.dart';
import 'package:sizer/sizer.dart';

import '../common_things/analytics.dart';

class StartupScreen extends StatefulWidget {
  static const routeName = '/startup_screen';
  @override
  State<StartupScreen> createState() => _StartupScreenState();
}

class _StartupScreenState extends State<StartupScreen> {
  /////
  bool _isAnalytics = true;
  late final MyAnalytics myAnalytics;
  //////
  @override
  Widget build(BuildContext context) {
    //////////
    if (_isAnalytics) {
      myAnalytics = Provider.of<MyAnalytics>(context, listen: false);
      myAnalytics.setLogEvent('StartupScreen', {});
      _isAnalytics = false;
    }
    ////////////
    return Scaffold(
      body: Container(
        height: 100.h,
        width: 100.w,
        color: mainColor,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Image.asset(
              MyImages.k_500_500,
              width: 50.w,
            ),
            SizedBox(height: 1.h),
            // Spink
            SpinKitChasingDots(
              color: thirdColor,
              size: 10.w,
            ),
          ],
        ),
      ),
    );
  }
}
