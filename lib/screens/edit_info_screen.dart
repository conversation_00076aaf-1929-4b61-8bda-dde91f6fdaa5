import 'package:camera/camera.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:zupid/models/myUserInfo.dart';
import 'package:zupid/providers/auth_provider.dart';
import 'package:zupid/screens/choose_from_list_screen.dart';
import 'package:zupid/screens/choose_height_screen.dart';
import 'package:zupid/screens/user_profile_screen.dart';
import 'package:zupid/translations/locale_keys.g.dart';
import 'package:zupid/widgets/interests_tab.dart';
import 'package:zupid/widgets/personality_tab.dart';
import 'package:zupid/widgets/photos_container.dart';
import 'package:provider/provider.dart';
import 'package:sizer/sizer.dart';
import 'package:zupid/screens/verification_screen.dart';
import 'package:toggle_switch/toggle_switch.dart';

import 'package:zupid/common_things/my_colors.dart';
import 'package:zupid/common_things/my_constants.dart';
import 'package:zupid/common_things/my_fucntions.dart';
import 'package:zupid/screens/choose_city_screen.dart';

import '../common_things/analytics.dart';
import '../providers/database_provider.dart';

class EditInfoScreen extends StatefulWidget {
  static const routeName = '/edit-info-screen';

  @override
  State<EditInfoScreen> createState() => _EditInfoScreenState();
}

class _EditInfoScreenState extends State<EditInfoScreen>
    with SingleTickerProviderStateMixin {
  /////
  bool _isAnalytics = true;
  late final MyAnalytics myAnalytics;
  //////
  late TabController _tabController;
  bool _isLoading = false;
  //
  @override
  void initState() {
    super.initState();
    _tabController = TabController(
        length: 3, vsync: this); // 3 tabs: Looks, Personality, Interest
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    //////////
    if (_isAnalytics) {
      myAnalytics = Provider.of<MyAnalytics>(context, listen: false);
      myAnalytics.setLogEvent('EditInfoScreen', {});
      _isAnalytics = false;
    }
    ////////////
    // first load myuser info from dbprov
    final _myUserInfo =
        Provider.of<DatabaseProvider>(context, listen: true).getMyUserInfo;
    final _promptList =
        Provider.of<DatabaseProvider>(context, listen: true).getPromptList;
    //
    int verifyProfileStatus =
        Provider.of<DatabaseProvider>(context, listen: false).getVerifyProfile;
    //
    final _allPersonalityTraits =
        Provider.of<DatabaseProvider>(context, listen: false)
            .getAllPersonalityTraitsList;
    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        // iconTheme: const IconThemeData(color: Colors.grey),
        title: Text(
          LocaleKeys.EditInfoScreen_Edit_Info.tr(),
          // style: const TextStyle(
          //   color: Colors.grey,
          //   fontWeight: FontWeight.bold,
          // ),
        ),
      ),
      body: Stack(
        children: [
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  mainColor,
                  // mainColor,
                  mainContrastColor.withOpacity(0.1),
                ],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ),
            ),
          ),
          Padding(
            padding: EdgeInsets.fromLTRB(0, 15.h, 0, 0),
            child: TabBarView(
              controller: _tabController,
              children: [
                SingleChildScrollView(
                  child: Padding(
                    padding: EdgeInsets.all(2.w),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const PhotosContainer(
                          showOnly2: false,
                        ),
                        Padding(
                          padding: EdgeInsets.all(2.w),
                          child: Text(
                            '* Fake photos, memes, cartoon etc. is not allowed\n* Breaking above rules will led to permanent ban',
                            style: TextStyle(
                              fontSize: 8.sp,
                              color: Colors.grey,
                              fontWeight: FontWeight.w500,
                              fontStyle: FontStyle.italic,
                            ),
                          ),
                        ),
                        SizedBox(height: 1.h),
                        // Divider(height: 0),
                        SizedBox(height: 2.h),
                        Card(
                          color: Colors.transparent,
                          elevation: 1,
                          child: Container(
                            width: 100.w,
                            padding: EdgeInsets.all(1.w),
                            color: Colors.transparent,
                            child: ListTile(
                              minLeadingWidth: 0,
                              dense: true,
                              leading: const Icon(
                                Icons.person,
                                color: mainContrastColor,
                              ),
                              title: Text(
                                _myUserInfo.sex == 0
                                    ? "Woman"
                                    : _myUserInfo.sex == 1
                                        ? "Man"
                                        : "Non-binary",
                                style: const TextStyle(
                                  color: mainContrastColor,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              // trailing: Switch(
                              //   inactiveTrackColor: Colors.transparent,
                              //   activeColor: thirdColor,
                              //   activeTrackColor: thirdColor.withOpacity(0.5),
                              //   value: _myUserInfo.isHideName,
                              //   onChanged: (bool value) async {
                              //     if (!_myUserInfo.isHideName) {
                              //       callToast(
                              //           "Only your initials will be visible.");
                              //     } else {
                              //       callToast(
                              //           "Your full name will be visible.");
                              //     }

                              //     setState(() {
                              //       _isLoading = true;
                              //     });
                              //     await Provider.of<DatabaseProvider>(context,
                              //             listen: false)
                              //         .updateHideName();
                              //     setState(() {
                              //       _isLoading = false;
                              //     });
                              //   },
                              // ),
                            ),
                          ),
                        ),
                        if (_myUserInfo.isVerified == false)
                          Card(
                            color: Colors.transparent,
                            elevation: 1,
                            child: Container(
                              width: 100.w,
                              padding: EdgeInsets.all(1.w),
                              // decoration: BoxDecoration(
                              //   color: Colors.transparent,
                              //   border: Border.all(color: mainContrastColor),
                              //   borderRadius: BorderRadius.circular(
                              //     2.h,
                              //   ),
                              // ),
                              child: ListTile(
                                minLeadingWidth: 0,
                                dense: true,
                                onTap: () async {
                                  //
                                  setState(() {
                                    _isLoading = true;
                                  });
                                  final cameras = await availableCameras();
                                  //
                                  final firstCamera = cameras.firstWhere(
                                      (camera) =>
                                          camera.lensDirection ==
                                          CameraLensDirection.front);
                                  //
                                  setState(() {
                                    _isLoading = false;
                                  });

                                  Navigator.of(context).pushNamed(
                                    VerificationScreen.routeName,
                                    arguments: [firstCamera],
                                  );
                                },
                                leading: const Icon(
                                  Icons.verified,
                                  color: mainContrastColor,
                                ),
                                title: Text(
                                  verifyProfileStatus == 0
                                      ? LocaleKeys
                                              .EditInfoScreen_Verification_under_process
                                          .tr()
                                      : LocaleKeys
                                              .EditInfoScreen_Verify_your_profile
                                          .tr(),
                                  style: const TextStyle(
                                    color: mainContrastColor,
                                    fontWeight: FontWeight.w400,
                                  ),
                                ),
                              ),
                            ),
                          ),

                        if (_myUserInfo.isVerified)
                          Card(
                            color: Colors.transparent,
                            elevation: 1,
                            child: Container(
                              width: 100.w,
                              padding: EdgeInsets.all(1.w),
                              color: Colors.transparent,
                              child: ListTile(
                                minLeadingWidth: 0,
                                dense: true,
                                onTap: () {
                                  // _showModal(context, _myUserInfo.userName);
                                },
                                leading: const Icon(
                                  Icons.verified,
                                  color: Colors.blue,
                                ),
                                title: Text(
                                  LocaleKeys.EditInfoScreen_Profile_Verified
                                      .tr(),
                                  style: const TextStyle(
                                    color: mainContrastColor,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ),
                          ),

                        // Hide Name Toggle
                        Card(
                          color: Colors.transparent,
                          elevation: 1,
                          child: Container(
                            width: 100.w,
                            padding: EdgeInsets.all(1.w),
                            color: Colors.transparent,
                            child: ListTile(
                              minLeadingWidth: 0,
                              dense: true,
                              leading: const Icon(
                                Icons.visibility_off,
                                color: mainContrastColor,
                              ),
                              title: Text(
                                "Hide Your Name",
                                style: const TextStyle(
                                  color: mainContrastColor,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              trailing: Switch(
                                inactiveTrackColor: Colors.transparent,
                                activeColor: thirdColor,
                                activeTrackColor: thirdColor.withOpacity(0.5),
                                value: _myUserInfo.isHideName,
                                onChanged: (bool value) async {
                                  if (!_myUserInfo.isHideName) {
                                    callToast(
                                        "Only your initials will be visible.");
                                  } else {
                                    callToast(
                                        "Your full name will be visible.");
                                  }

                                  setState(() {
                                    _isLoading = true;
                                  });
                                  await Provider.of<DatabaseProvider>(context,
                                          listen: false)
                                      .updateHideName();
                                  setState(() {
                                    _isLoading = false;
                                  });
                                },
                              ),
                            ),
                          ),
                        ),

                        SizedBox(height: 2.h),
                        PersonalInfoWidget(myUserInfo: _myUserInfo),
                        SizedBox(height: 2.h),
                        LifeStyleWidget(myUserInfo: _myUserInfo),
                        SizedBox(height: 2.h),
                        BeliefsWidget(myUserInfo: _myUserInfo),
                        SizedBox(height: 2.h),
                      ],
                    ),
                  ),
                ),
                InterestTab(
                  myUserInfo: _myUserInfo,
                  isEditActive: true,
                ),
                PersonalityTab(
                  userInfo: _myUserInfo,
                  // allPersonalityTraits: _allPersonalityTraits,
                  isEditActive: true,
                ),
              ],
            ),
          ),
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: Align(
              alignment: Alignment.topCenter,
              child: Column(
                children: [
                  //
                  SizedBox(
                    height: 0.1.h,
                  ),
                  Divider(
                    height: 0,
                    color: mainContrastColor,
                  ),
                  ListTile(
                    minLeadingWidth: 0,
                    dense: true,
                    onTap: () {
                      Navigator.of(context).pushNamed(
                          UserProfileScreen.routeName,
                          arguments: [_myUserInfo.userId, true]);
                    },
                    leading: const Icon(
                      Icons.preview_rounded,
                      color: mainContrastColor,
                    ),
                    title: const Text(
                      "Preview Your Profile",
                      style: TextStyle(
                        color: mainContrastColor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  Divider(
                    height: 0,
                    color: mainContrastColor,
                  ),
                  SizedBox(height: 1.h),
                  //
                  Padding(
                    padding: EdgeInsets.fromLTRB(0, 1.h, 0, 2.h),
                    child: Container(
                      width: 80.w,
                      height: 5.h,
                      decoration: BoxDecoration(
                        gradient: const LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: [mainContrastColor, thirdColor]),
                        borderRadius: BorderRadius.circular(
                            5.w), // Adjust for capsule shape
                      ),
                      child: Padding(
                        padding: EdgeInsets.symmetric(
                            vertical: 2.w, horizontal: 1.w),
                        child: TabBar(
                          dividerColor: Colors.transparent,
                          controller: _tabController,
                          unselectedLabelColor:
                              mainColor, // Set text color for unselected tabs
                          labelColor:
                              Colors.white, // Set text color for selected tab
                          indicatorColor:
                              Colors.transparent, // Hide default indicator
                          // indicatorSize: TabBarIndicatorSize.tab,
                          tabs: [
                            Tab(
                                child: Center(
                                    child: Text("Looks",
                                        style: TextStyle(fontSize: 9.sp)))),
                            Tab(
                                child: Center(
                                    child: Text("Interest",
                                        style: TextStyle(fontSize: 9.sp)))),
                            Tab(
                                child: Center(
                                    child: Text("Personality",
                                        style: TextStyle(fontSize: 9.sp)))),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          )
        ],
      ),
    );
  }
}

class PersonalInfoWidget extends StatelessWidget {
  const PersonalInfoWidget({
    super.key,
    required MyUserInfo myUserInfo,
  }) : _myUserInfo = myUserInfo;

  final MyUserInfo _myUserInfo;
  String? getKeyFromValue(Map<dynamic, dynamic> map, int value) {
    return map.keys.firstWhere((key) => map[key] == value);
  }

  @override
  Widget build(BuildContext context) {
    const listTextColor = mainContrastColor;
    final authProv = Provider.of<AuthProvider>(context, listen: false);
    return Card(
      color: Colors.transparent,
      elevation: 1,
      child: Container(
        width: 100.w,
        padding: EdgeInsets.all(3.w),
        color: Colors.transparent,
        child: Column(
          children: [
            ListTile(
              minLeadingWidth: 0,
              dense: true,
              onTap: () {
                Navigator.of(context).pushNamed(ChooseHeightScreen.routename);
              },
              leading: const Icon(Icons.height_rounded, color: listTextColor),
              title: Text(
                _myUserInfo.height.toString() == '0'
                    ? "Select height"
                    : _myUserInfo.height.toString() + ' cm',
                style: TextStyle(
                  color: listTextColor,
                  fontWeight: _myUserInfo.height.toString() == "0"
                      ? FontWeight.normal
                      : FontWeight.bold,
                ),
              ),
              trailing: const Icon(
                Icons.arrow_forward_ios_rounded,
                color: listTextColor,
              ),
            ),
            ListTile(
              minLeadingWidth: 0,
              dense: true,
              onTap: () {
                Navigator.of(context).pushNamed(ChooseCityScreen.routeName);
              },
              leading: const Icon(Icons.location_city, color: listTextColor),
              title: Text(
                _myUserInfo.cityName == ''
                    ? "Enter city"
                    : _myUserInfo.cityName,
                style: TextStyle(
                  color: listTextColor,
                  fontWeight: _myUserInfo.cityName == ""
                      ? FontWeight.normal
                      : FontWeight.bold,
                ),
              ),
              trailing: const Icon(
                Icons.arrow_forward_ios_rounded,
                color: listTextColor,
              ),
            ),
            ListTile(
              minLeadingWidth: 0,
              dense: true,
              onTap: () {
                _editWork(context, _myUserInfo.work);
              },
              leading: const Icon(
                Icons.work_rounded,
                color: listTextColor,
              ),
              title: Text(
                _myUserInfo.work == ''
                    ? LocaleKeys.EditInfoScreen_Enter_work.tr()
                    : _myUserInfo.work,
                style: TextStyle(
                  color: listTextColor,
                  fontWeight: _myUserInfo.work == ""
                      ? FontWeight.normal
                      : FontWeight.bold,
                ),
              ),
              trailing: const Icon(
                Icons.arrow_forward_ios_rounded,
                color: listTextColor,
              ),
            ),
            ListTile(
              minLeadingWidth: 0,
              dense: true,
              onTap: () {
                _editSchool(context, _myUserInfo.school);
              },
              leading: Icon(
                Icons.apartment_rounded,
                color: listTextColor,
              ),
              title: Text(
                _myUserInfo.school == ''
                    ? LocaleKeys.EditInfoScreen_Enter_school.tr()
                    : _myUserInfo.school,
                style: TextStyle(
                  color: listTextColor,
                  fontWeight: _myUserInfo.school == ""
                      ? FontWeight.normal
                      : FontWeight.bold,
                ),
              ),
              trailing: const Icon(
                Icons.arrow_forward_ios_rounded,
                color: listTextColor,
              ),
            ),
            ListTile(
              minLeadingWidth: 0,
              dense: true,
              onTap: () {
                Navigator.of(context)
                    .pushNamed(ChooseFromListScreen.routeName, arguments: [
                  'Education Level',
                  authProv.getEducation,
                  _myUserInfo.educationLevel,
                  'educational_level',
                  userInfoEducationalLevelRoute
                ]);
              },
              leading: Icon(
                Icons.school_rounded,
                color: listTextColor,
              ),
              title: Text(
                getKeyFromValue(
                    authProv.getEducation, _myUserInfo.educationLevel)!,
                style: TextStyle(
                  color: listTextColor,
                  fontWeight: _myUserInfo.educationLevel.toString() == "0"
                      ? FontWeight.normal
                      : FontWeight.bold,
                ),
              ),
              trailing: const Icon(
                Icons.arrow_forward_ios_rounded,
                color: listTextColor,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class LifeStyleWidget extends StatelessWidget {
  const LifeStyleWidget({
    super.key,
    required MyUserInfo myUserInfo,
  }) : _myUserInfo = myUserInfo;
  final MyUserInfo _myUserInfo;

  @override
  Widget build(BuildContext context) {
    final authProv = Provider.of<AuthProvider>(context, listen: false);
    const listTextColor = mainContrastColor;
    return Card(
      color: Colors.transparent,
      elevation: 1,
      child: Container(
        width: 100.w,
        padding: EdgeInsets.all(3.w),
        color: Colors.transparent,
        child: Column(
          children: [
            ListTile(
              minLeadingWidth: 0,
              dense: true,
              onTap: () {
                Navigator.of(context)
                    .pushNamed(ChooseFromListScreen.routeName, arguments: [
                  'Drinking',
                  authProv.getDrinking,
                  _myUserInfo.drinking,
                  'drinking',
                  userInfoDrinkingRoute,
                ]);
              },
              leading: const Icon(
                Icons.wine_bar_rounded,
                color: listTextColor,
              ),
              title: Text(
                getKeyFromValue(authProv.getDrinking, _myUserInfo.drinking)!,
                style: TextStyle(
                  color: listTextColor,
                  fontWeight: _myUserInfo.drinking.toString() == "0"
                      ? FontWeight.normal
                      : FontWeight.bold,
                ),
              ),
              trailing: const Icon(
                Icons.arrow_forward_ios_rounded,
                color: listTextColor,
              ),
            ),
            ListTile(
              minLeadingWidth: 0,
              dense: true,
              onTap: () {
                Navigator.of(context)
                    .pushNamed(ChooseFromListScreen.routeName, arguments: [
                  'Smoking',
                  authProv.getSmoking,
                  _myUserInfo.smoking,
                  'smoking',
                  userInfoSmokingRoute,
                ]);
              },
              leading: const Icon(
                Icons.smoking_rooms_rounded,
                color: listTextColor,
              ),
              title: Text(
                getKeyFromValue(authProv.getSmoking, _myUserInfo.smoking)!,
                style: TextStyle(
                  color: listTextColor,
                  fontWeight: _myUserInfo.smoking.toString() == "0"
                      ? FontWeight.normal
                      : FontWeight.bold,
                ),
              ),
              trailing: const Icon(
                Icons.arrow_forward_ios_rounded,
                color: listTextColor,
              ),
            ),
            ListTile(
              minLeadingWidth: 0,
              dense: true,
              onTap: () {
                Navigator.of(context).pushNamed(ChooseFromListScreen.routeName,
                    arguments: [
                      'Drugs',
                      authProv.getDrugs,
                      _myUserInfo.drugs,
                      'drugs',
                      userInfoDrugsRoute
                    ]);
              },
              leading: FaIcon(
                FontAwesomeIcons.capsules,
                color: listTextColor,
              ),
              title: Text(
                getKeyFromValue(authProv.getDrugs, _myUserInfo.drugs)!,
                style: TextStyle(
                  color: listTextColor,
                  fontWeight: _myUserInfo.drugs.toString() == "0"
                      ? FontWeight.normal
                      : FontWeight.bold,
                ),
              ),
              trailing: const Icon(
                Icons.arrow_forward_ios_rounded,
                color: listTextColor,
              ),
            ),
            ListTile(
              minLeadingWidth: 0,
              dense: true,
              onTap: () {
                Navigator.of(context)
                    .pushNamed(ChooseFromListScreen.routeName, arguments: [
                  'Marijuana',
                  authProv.getMarijuana,
                  _myUserInfo.marijuana,
                  'marijuana',
                  userInfoMarijuanaRoute
                ]);
              },
              leading: Icon(
                Icons.grass_rounded,
                color: listTextColor,
              ),
              title: Text(
                getKeyFromValue(authProv.getMarijuana, _myUserInfo.marijuana)!,
                style: TextStyle(
                  color: listTextColor,
                  fontWeight: _myUserInfo.marijuana.toString() == "0"
                      ? FontWeight.normal
                      : FontWeight.bold,
                ),
              ),
              trailing: const Icon(
                Icons.arrow_forward_ios_rounded,
                color: listTextColor,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class BeliefsWidget extends StatelessWidget {
  const BeliefsWidget({
    super.key,
    required MyUserInfo myUserInfo,
  }) : _myUserInfo = myUserInfo;
  final MyUserInfo _myUserInfo;

  String? getKeyFromValue(Map<dynamic, dynamic> map, int value) {
    return map.keys.firstWhere((key) => map[key] == value);
  }

  @override
  Widget build(BuildContext context) {
    final authProv = Provider.of<AuthProvider>(context, listen: false);
    const listTextColor = mainContrastColor;

    return Card(
      color: Colors.transparent,
      elevation: 1,
      child: Container(
        width: 100.w,
        padding: EdgeInsets.all(3.w),
        color: Colors.transparent,
        child: Column(
          children: [
            ListTile(
              minLeadingWidth: 0,
              dense: true,
              onTap: () {
                Navigator.of(context)
                    .pushNamed(ChooseFromListScreen.routeName, arguments: [
                  'Religion',
                  authProv.getReligion,
                  _myUserInfo.religion,
                  'religion',
                  userInfoReligionRoute,
                ]);
              },
              leading: const Icon(
                Icons.temple_hindu_rounded,
                color: mainContrastColor,
              ),
              title: Text(
                getKeyFromValue(authProv.getReligion, _myUserInfo.religion)!,
                style: TextStyle(
                  color: mainContrastColor,
                  fontWeight: _myUserInfo.religion.toString() == "0"
                      ? FontWeight.normal
                      : FontWeight.bold,
                ),
              ),
              trailing: const Icon(
                Icons.arrow_forward_ios_rounded,
                color: mainContrastColor,
              ),
            ),
            ListTile(
              minLeadingWidth: 0,
              dense: true,
              onTap: () {
                Navigator.of(context)
                    .pushNamed(ChooseFromListScreen.routeName, arguments: [
                  'Zodiac',
                  authProv.getZodiac,
                  _myUserInfo.zodiac,
                  'zodiac',
                  userInfoZodiacRoute,
                ]);
              },
              leading: FaIcon(
                FontAwesomeIcons.star,
                color: mainContrastColor,
              ),
              title: Text(
                getKeyFromValue(authProv.getZodiac, _myUserInfo.zodiac)!,
                style: TextStyle(
                  color: mainContrastColor,
                  fontWeight: _myUserInfo.zodiac.toString() == "0"
                      ? FontWeight.normal
                      : FontWeight.bold,
                ),
              ),
              trailing: const Icon(
                Icons.arrow_forward_ios_rounded,
                color: mainContrastColor,
              ),
            ),
            ListTile(
              minLeadingWidth: 0,
              dense: true,
              onTap: () {
                Navigator.of(context)
                    .pushNamed(ChooseFromListScreen.routeName, arguments: [
                  'Sexuality',
                  authProv.getSexuality,
                  _myUserInfo.sexuality,
                  'sexuality',
                  userInfoSexualityRoute,
                ]);
              },
              leading: const Icon(
                Icons.male_rounded,
                color: mainContrastColor,
              ),
              title: Text(
                getKeyFromValue(authProv.getSexuality, _myUserInfo.sexuality)!,
                style: TextStyle(
                  color: mainContrastColor,
                  fontWeight: _myUserInfo.sexuality.toString() == "0"
                      ? FontWeight.normal
                      : FontWeight.bold,
                ),
              ),
              trailing: const Icon(
                Icons.arrow_forward_ios_rounded,
                color: mainContrastColor,
              ),
            ),
            ListTile(
              minLeadingWidth: 0,
              dense: true,
              onTap: () {
                Navigator.of(context)
                    .pushNamed(ChooseFromListScreen.routeName, arguments: [
                  'Children',
                  authProv.getChildren,
                  _myUserInfo.children,
                  'children',
                  userInfoChildrenRoute,
                ]);
              },
              leading: const Icon(
                Icons.child_friendly_rounded,
                color: mainContrastColor,
              ),
              title: Text(
                getKeyFromValue(authProv.getChildren, _myUserInfo.children)!,
                style: TextStyle(
                  color: mainContrastColor,
                  fontWeight: _myUserInfo.children.toString() == "0"
                      ? FontWeight.normal
                      : FontWeight.bold,
                ),
              ),
              trailing: const Icon(
                Icons.arrow_forward_ios_rounded,
                color: mainContrastColor,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

Future<void> _editWork(context, String _work) async {
  var answer = 0;
  final _formKey = GlobalKey<FormState>();
  if (_work == '' || _work == 'Not Working') {
    _work = 'Not Working';
    answer = 1;
  }

  return showModalBottomSheet(
      isScrollControlled: true,
      context: context,
      builder: (context) {
        Future<void> _saveForm() async {}
        return StatefulBuilder(
            builder: (BuildContext context, StateSetter mystate) {
          return Container(
            color: mainColor,
            child: SingleChildScrollView(
              child: Wrap(
                children: [
                  Padding(
                    padding: MediaQuery.of(context).viewInsets,
                    child: Container(
                      child: Padding(
                        padding: EdgeInsets.all(1.h),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                          children: [
                            SizedBox(
                              height: 5.h,
                            ),
                            Center(
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Text(
                                    LocaleKeys.EditInfoScreen_Are_you_working
                                        .tr(),
                                    style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                      color: mainContrastColor,
                                    ),
                                  ),
                                  SizedBox(
                                    width: 10.w,
                                  ),
                                  ToggleSwitch(
                                    initialLabelIndex: answer,
                                    totalSwitches: 2,
                                    labels: [
                                      LocaleKeys.Common_Yes.tr(),
                                      LocaleKeys.Common_No.tr(),
                                    ],
                                    customTextStyles: [
                                      TextStyle(color: mainColor),
                                      TextStyle(color: mainColor)
                                    ],
                                    activeBgColors: const [
                                      [thirdColor],
                                      [thirdColor],
                                      // [Colors.green],
                                      // [Colors.green]
                                    ],
                                    inactiveBgColor: Colors.white,
                                    onToggle: (index) {
                                      answer = index!;
                                      if (answer == 1) {
                                        _work = 'Not Working';
                                      } else {
                                        _work = '';
                                      }
                                      // print('$answer : $_work');
                                      // print(answer);
                                      // print('switched to: $index');
                                      mystate(
                                        () {},
                                      );
                                    },
                                  ),
                                ],
                              ),
                            ),
                            SizedBox(
                              height: 5.h,
                            ),
                            // if (answer == 0)
                            Form(
                              key: _formKey,
                              child: TextFormField(
                                enabled: answer == 0 ? true : false,
                                initialValue: _work,
                                enableSuggestions: false,
                                autocorrect: false,
                                decoration: InputDecoration(
                                  labelText: LocaleKeys
                                      .EditInfoScreen_Company_or_Industry.tr(),
                                  labelStyle: TextStyle(
                                    color:
                                        mainContrastColor, // Use your main theme color
                                    fontWeight: FontWeight.bold,
                                  ),
                                  filled: true,
                                  fillColor:
                                      mainColor, // Light background color
                                  border: OutlineInputBorder(
                                    borderSide: BorderSide(
                                        color: mainContrastColor, width: 1.5),
                                    borderRadius: BorderRadius.circular(
                                        10), // Rounded borders
                                  ),
                                  focusedBorder: OutlineInputBorder(
                                    borderSide: BorderSide(
                                        color: mainContrastColor, width: 2),
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                  enabledBorder: OutlineInputBorder(
                                    borderSide: BorderSide(
                                        color: mainContrastColor, width: 1.5),
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                  errorBorder: OutlineInputBorder(
                                    borderSide: BorderSide(
                                        color: thirdColor, width: 1.5),
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                  contentPadding: EdgeInsets.symmetric(
                                      vertical: 16, horizontal: 16),
                                  errorMaxLines: 2,
                                ),
                                textInputAction: TextInputAction.next,
                                inputFormatters: <TextInputFormatter>[
                                  LengthLimitingTextInputFormatter(32),
                                ],
                                validator: (value) {
                                  if (value == null ||
                                      containsOnlySpaces(value)) {
                                    return 'Please enter Company';
                                  }
                                },
                                onSaved: (value) {
                                  _work = value!;
                                },
                              ),
                            ),
                            Padding(
                              padding: EdgeInsets.all(5.sp),
                              child: ElevatedButton(
                                  style: ButtonStyle(
                                      backgroundColor:
                                          MaterialStatePropertyAll(thirdColor)),
                                  onPressed: () async {
                                    final isValid =
                                        _formKey.currentState!.validate();
                                    if (!isValid) {
                                      return;
                                    }
                                    _formKey.currentState!.save();
                                    //
                                    if (answer == 1) {
                                      _work = 'Not Working';
                                    } else {
                                      //
                                    }
                                    Map<String, String> _payloadData = {
                                      'work': _work
                                    };
                                    Provider.of<DatabaseProvider>(context,
                                            listen: false)
                                        .updateUserInfo(
                                            userInfoWorkRoute, _payloadData);
                                    //
                                    Navigator.pop(context);
                                  },
                                  child: Text(
                                    LocaleKeys.Common_Submit.tr(),
                                    style: TextStyle(color: mainColor),
                                  )),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        });
      });
}

Future<void> _editSchool(context, String _school) async {
  final _formKey = GlobalKey<FormState>();
  if (_school == '') {
    _school = '';
  }
  return showModalBottomSheet(
      isScrollControlled: true,
      context: context,
      builder: (context) {
        return StatefulBuilder(
            builder: (BuildContext context, StateSetter mystate) {
          return Container(
            color: mainColor,
            child: SingleChildScrollView(
              child: Wrap(
                children: [
                  Padding(
                    padding: MediaQuery.of(context).viewInsets,
                    child: Container(
                      child: Padding(
                        padding: EdgeInsets.all(2.h),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                          children: [
                            SizedBox(
                              height: 5.h,
                            ),
                            // if (answer == 0)
                            Form(
                              key: _formKey,
                              child: TextFormField(
                                initialValue: _school,
                                enableSuggestions: false,
                                autocorrect: false,
                                decoration: InputDecoration(
                                  labelText: LocaleKeys
                                          .EditInfoScreen_Enter_college_or_school
                                      .tr(),
                                  labelStyle: TextStyle(
                                    color:
                                        mainContrastColor, // Use your main theme color
                                    fontWeight: FontWeight.bold,
                                  ),
                                  filled: true,
                                  fillColor:
                                      mainColor, // Light background color
                                  border: OutlineInputBorder(
                                    borderSide: BorderSide(
                                        color: mainContrastColor, width: 1.5),
                                    borderRadius: BorderRadius.circular(
                                        10), // Rounded borders
                                  ),
                                  focusedBorder: OutlineInputBorder(
                                    borderSide: BorderSide(
                                        color: mainContrastColor, width: 2),
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                  enabledBorder: OutlineInputBorder(
                                    borderSide: BorderSide(
                                        color: mainContrastColor, width: 1.5),
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                  errorBorder: OutlineInputBorder(
                                    borderSide: BorderSide(
                                        color: thirdColor, width: 1.5),
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                  contentPadding: EdgeInsets.symmetric(
                                      vertical: 16, horizontal: 16),
                                  errorMaxLines: 2,
                                ),
                                textInputAction: TextInputAction.next,
                                inputFormatters: <TextInputFormatter>[
                                  // FilteringTextInputFormatter.allow(
                                  //     RegExp("[0-9a-zA-Z\u0900-\u097F]")),
                                  LengthLimitingTextInputFormatter(32),
                                ],
                                validator: (value) {
                                  if (value == null ||
                                      containsOnlySpaces(value)) {
                                    return LocaleKeys
                                        .EditInfoScreen_Enter_school.tr();
                                  }
                                },
                                onSaved: (value) {
                                  _school = value!;
                                },
                              ),
                            ),
                            Padding(
                              padding: EdgeInsets.all(5.sp),
                              child: ElevatedButton(
                                  style: ButtonStyle(
                                      backgroundColor:
                                          MaterialStatePropertyAll(thirdColor)),
                                  onPressed: () async {
                                    final isValid =
                                        _formKey.currentState!.validate();
                                    if (!isValid) {
                                      return;
                                    }
                                    _formKey.currentState!.save();
                                    Map<String, String> _payloadData = {
                                      'school': _school
                                    };
                                    Provider.of<DatabaseProvider>(context,
                                            listen: false)
                                        .updateUserInfo(
                                            userInfoSchoolRoute, _payloadData);

                                    Navigator.pop(context);
                                  },
                                  child: Text(
                                    LocaleKeys.Common_Submit.tr(),
                                    style: TextStyle(color: mainColor),
                                  )),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        });
      });
}
