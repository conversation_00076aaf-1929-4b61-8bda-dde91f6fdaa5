import 'package:flutter/material.dart';
import 'package:flutter_typeahead/flutter_typeahead.dart';
import 'package:provider/provider.dart';
import 'package:sizer/sizer.dart';
import 'package:zupid/common_things/my_colors.dart';
import 'package:zupid/providers/auth_provider.dart';
import 'package:zupid/widgets/loading_widget.dart';

import '../common_things/analytics.dart';
import '../models/myUserInfo.dart';
import '../models/myUserInfo2.dart';
import '../providers/database_provider.dart';

class ChangeCityScreen extends StatefulWidget {
  static const routeName = '/change-city-screen';

  @override
  State<ChangeCityScreen> createState() => _ChangeCityScreenState();
}

class _ChangeCityScreenState extends State<ChangeCityScreen> {
  /////
  bool _isAnalytics = true;
  late final MyAnalytics myAnalytics;
  //////
  bool _firstRun = true;
  bool _isLoading = false;
  final TextEditingController _typeAheadController = TextEditingController();
  //
  bool isPassport = false;
  String selectedCity = '';
  String selectedState = '';
  String selectedCountry = '';
  double selectedLatitude = 0;
  double selectedLongitude = 0;
  //
  @override
  Widget build(BuildContext context) {
    if (_isAnalytics) {
      myAnalytics = Provider.of<MyAnalytics>(context, listen: false);
      myAnalytics.setLogEvent('ChangeCityScreen', {});
      _isAnalytics = false;
    }
    //
    MyUserInfo _myUserInfo =
        Provider.of<DatabaseProvider>(context, listen: false).getMyUserInfo;
    MyUserInfo2 _myUserInfo2 =
        Provider.of<DatabaseProvider>(context, listen: false).getMyUserInfo2;
    //
    if (_firstRun) {
      _firstRun = false;
      //
      isPassport = _myUserInfo.isPassport;
      if (isPassport) {
        selectedCity = _myUserInfo2.passportCity;
        selectedState = _myUserInfo2.passportState;
        selectedCountry = _myUserInfo2.passportCountry;
        selectedLatitude = _myUserInfo.latitude;
        selectedLongitude = _myUserInfo.longitude;
      }
    }
    //

    return WillPopScope(
      onWillPop: () async {
        // inside will pop
        setState(() {
          _isLoading = true;
        });
        //
        Map<String, dynamic> jsonData = {
          'is_passport': isPassport,
          'latitude': selectedLatitude,
          'longitude': selectedLongitude,
          'passport_city': selectedCity,
          'passport_state': selectedState,
          'passport_country': selectedCountry,
        };
        await Provider.of<DatabaseProvider>(context, listen: false)
            .changePassportLocation(jsonData);
        //
        setState(() {
          _isLoading = false;
        });
        return true;
      },
      child: Scaffold(
        backgroundColor: Colors.black,
        appBar: AppBar(
          // backgroundColor: Colors.white,
          elevation: 0,
          // leading: IconButton(
          //   icon: Icon(
          //     Icons.arrow_back_ios,
          //     color: Colors.black,
          //     size: 6.w,
          //   ),
          //   onPressed: () {
          //     Navigator.of(context).pop();
          //   },
          // ),
        ),
        body: _isLoading
            ? LoadingWidget()
            : SingleChildScrollView(
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 5.w),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(height: 5.h),
                      ListTile(
                        title: Text(
                          "Use Current Location",
                          style: TextStyle(
                            fontSize: 13.sp,
                            fontWeight: FontWeight.w600,
                            color:
                                !isPassport ? mainContrastColor : Colors.grey,
                          ),
                        ),
                        trailing: Icon(
                          Icons.my_location,
                          color: !isPassport ? mainContrastColor : Colors.grey,
                          size: 6.w,
                        ),
                        onTap: () {
                          // _setLocation("Current Location");
                          setState(() {
                            isPassport = false;
                            selectedLatitude = Provider.of<AuthProvider>(
                                    context,
                                    listen: false)
                                .getLatitude;
                            selectedLongitude = Provider.of<AuthProvider>(
                                    context,
                                    listen: false)
                                .getLongitude;
                            //
                            selectedCity = "";
                            selectedState = "";
                            selectedCountry = "";
                          });
                          // Navigator.of(context).pop();
                        },
                      ),
                      SizedBox(height: 2.h),
                      Center(
                        child: Text(
                          "Or",
                          style: TextStyle(
                            fontSize: 14.sp,
                            color: Colors.grey,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      SizedBox(height: 2.h),
                      ListTile(
                        title: Text(
                          "Choose other city",
                          style: TextStyle(
                            fontSize: 13.sp,
                            fontWeight: FontWeight.w600,
                            color: isPassport ? mainContrastColor : Colors.grey,
                          ),
                        ),
                        trailing: Icon(
                          Icons.location_city_rounded,
                          color: isPassport ? mainContrastColor : Colors.grey,
                          size: 6.w,
                        ),
                        onTap: () {
                          // _setLocation("Current Location");
                          setState(() {
                            isPassport = true;
                          });

                          // Navigator.of(context).pop();
                        },
                      ),
                      if (isPassport)
                        Column(
                          children: [
                            SizedBox(
                              height: 3.h,
                            ),
                            Text(
                              selectedCity != ''
                                  ? '$selectedCity, $selectedState'
                                  : '',
                              style: TextStyle(
                                fontSize: 13.sp,
                                fontWeight: FontWeight.w600,
                                color: isPassport == false
                                    ? Colors.grey
                                    : mainContrastColor,
                              ),
                            ),
                            SizedBox(height: 5.h),
                            TypeAheadField(
                              textFieldConfiguration: TextFieldConfiguration(
                                controller: _typeAheadController,
                                decoration: InputDecoration(
                                  border: OutlineInputBorder(),
                                  labelText: 'Search city',
                                  prefixIcon: Icon(Icons.search),
                                  contentPadding: EdgeInsets.all(2.sp),
                                ),
                              ),
                              suggestionsCallback: (pattern) async {
                                return await Provider.of<DatabaseProvider>(
                                        context,
                                        listen: false)
                                    .searchQuery(pattern);
                              },
                              transitionBuilder:
                                  (context, suggestionsBox, controller) {
                                return suggestionsBox;
                              },
                              itemBuilder: (context, suggestion) {
                                final cityName = suggestion['city_name'];
                                final stateName = suggestion['state_name'];
                                final countryName = suggestion['country_name'];
                                final latitude = suggestion['latitude'];
                                final longitude = suggestion['longitude'];
                                // print('seach list');
                                return Column(
                                  children: [
                                    ListTile(
                                      onTap: () {
                                        _typeAheadController.clear();
                                        setState(() {
                                          selectedLatitude = latitude;
                                          selectedLongitude = longitude;
                                          selectedCity = cityName;
                                          selectedState = stateName;
                                          selectedCountry = countryName;
                                        });
                                      },
                                      title: Text(
                                        '$cityName, $stateName',
                                        style: TextStyle(
                                            color: mainColor,
                                            fontSize: 12.sp,
                                            fontWeight: FontWeight.bold),
                                      ),
                                      subtitle: Text(
                                        countryName,
                                        style: TextStyle(
                                            color: mainColor, fontSize: 12.sp),
                                      ),
                                    ),
                                    Divider(
                                      thickness: .4.sp,
                                    )
                                  ],
                                );
                              },
                              onSuggestionSelected: (suggestion) {
                                setState(() {});
                              },
                            ),
                          ],
                        ),
                    ],
                  ),
                ),
              ),
      ),
    );
  }
}
