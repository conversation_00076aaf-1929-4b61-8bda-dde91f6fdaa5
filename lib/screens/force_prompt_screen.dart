import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:zupid/common_things/my_colors.dart';
import 'package:zupid/common_things/my_fucntions.dart';
import 'package:zupid/models/myPromptList.dart';
import 'package:zupid/providers/database_provider.dart';
import 'package:provider/provider.dart';
import 'package:zupid/models/myUserInfo.dart';
import 'package:sizer/sizer.dart';

class ForcePromptScreenScreen extends StatefulWidget {
  static const routeName = '/force-prompt-screen';

  @override
  State<ForcePromptScreenScreen> createState() =>
      _ForcePromptScreenScreenState();
}

class _ForcePromptScreenScreenState extends State<ForcePromptScreenScreen> {
  bool runOnce = true;
  MyUserInfo? myUserInfo;

  @override
  Widget build(BuildContext context) {
    myUserInfo =
        Provider.of<DatabaseProvider>(context, listen: true).getMyUserInfo;
    final _promptList =
        Provider.of<DatabaseProvider>(context, listen: false).getPromptList;
    return Scaffold(
      backgroundColor: mainColor,
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.transparent,
        centerTitle: true,
        title: Text(
          'Share More About You',
          style: TextStyle(
            fontSize: 16.sp,
            color: thirdColor,
            fontWeight: FontWeight.w600,
          ),
        ),
        systemOverlayStyle: const SystemUiOverlayStyle(
          statusBarColor: mainColor,
          statusBarIconBrightness: Brightness.light,
        ),
      ),
      body: Container(
        height: 100.h,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              mainColor,
              mainContrastColor.withOpacity(0.1),
            ],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
        ),
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 5.w, vertical: 2.h),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text(
                'Answer prompts to help others know you better',
                style: TextStyle(
                  fontSize: 12.sp,
                  color: mainContrastColor.withOpacity(0.8),
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 4.h),
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      _buildPromptCard(
                        context,
                        _promptList,
                        1,
                        myUserInfo!.allInterests.prompts.length >= 1,
                      ),
                      SizedBox(height: 3.h),
                      _buildPromptCard(
                        context,
                        _promptList,
                        2,
                        myUserInfo!.allInterests.prompts.length >= 2,
                      ),
                      SizedBox(height: 3.h),
                      _buildPromptCard(
                        context,
                        _promptList,
                        3,
                        myUserInfo!.allInterests.prompts.length >= 3,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPromptCard(BuildContext context, List<MyPrompt> promptList,
      int promptNumber, bool hasPrompt) {
    return Container(
      decoration: BoxDecoration(
        color: mainColor.withOpacity(0.9),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: thirdColor.withOpacity(0.3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.all(3.w),
        child: hasPrompt
            ? PromptQuestionAnswerWidget(
                promptList: promptList,
                promptNumber: promptNumber,
                isEditActive: true,
                varUserInfo: myUserInfo!,
              )
            : AddPromptWidget(promptNumber: promptNumber),
      ),
    );
  }

  // List<dynamic> _getCategories(List<Map<String, dynamic>> traits) {
  //   return traits.map((trait) => trait['category']).toSet().toList();
  // }
}

// Future<void> _editAboutMe(context, String _aboutMe) async {
//   final _formKey = GlobalKey<FormState>();
//   if (_aboutMe == '') {
//     _aboutMe = '';
//   }
//   return showModalBottomSheet(
//     isScrollControlled: true,
//     context: context,
//     backgroundColor: Colors.transparent,
//     builder: (context) {
//       return StatefulBuilder(
//         builder: (BuildContext context, StateSetter mystate) {
//           return Container(
//             decoration: BoxDecoration(
//               color: mainColor,
//               borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
//               boxShadow: [
//                 BoxShadow(
//                   color: Colors.black.withOpacity(0.2),
//                   blurRadius: 20,
//                   spreadRadius: 5,
//                 ),
//               ],
//             ),
//             child: Padding(
//               padding: EdgeInsets.only(
//                 bottom: MediaQuery.of(context).viewInsets.bottom,
//                 left: 5.w,
//                 right: 5.w,
//                 top: 3.h,
//               ),
//               child: Column(
//                 mainAxisSize: MainAxisSize.min,
//                 children: [
//                   Text(
//                     'Edit About Me',
//                     style: TextStyle(
//                       fontSize: 16.sp,
//                       color: mainContrastColor,
//                       fontWeight: FontWeight.w600,
//                     ),
//                   ),
//                   SizedBox(height: 3.h),
//                   Form(
//                     key: _formKey,
//                     child: TextFormField(
//                       maxLines: 6,
//                       initialValue: _aboutMe,
//                       style: TextStyle(color: mainContrastColor),
//                       decoration: InputDecoration(
//                         labelText: 'About Myself',
//                         labelStyle: TextStyle(
//                           color: mainContrastColor.withOpacity(0.8),
//                           fontWeight: FontWeight.w500,
//                         ),
//                         filled: true,
//                         fillColor: mainColor.withOpacity(0.9),
//                         border: OutlineInputBorder(
//                           borderRadius: BorderRadius.circular(12),
//                           borderSide: BorderSide(
//                             color: mainContrastColor.withOpacity(0.5),
//                             width: 1.5,
//                           ),
//                         ),
//                         focusedBorder: OutlineInputBorder(
//                           borderRadius: BorderRadius.circular(12),
//                           borderSide: BorderSide(
//                             color: thirdColor,
//                             width: 2,
//                           ),
//                         ),
//                         contentPadding: EdgeInsets.all(16),
//                       ),
//                       validator: (value) {
//                         if (value == null || containsOnlySpaces(value)) {
//                           return LocaleKeys
//                               .EditInfoScreen_Please_enter_your_life_story.tr();
//                         }
//                       },
//                       onSaved: (value) {
//                         _aboutMe = value!;
//                       },
//                     ),
//                   ),
//                   SizedBox(height: 3.h),
//                   SizedBox(
//                     width: double.infinity,
//                     child: ElevatedButton(
//                       style: ElevatedButton.styleFrom(
//                         backgroundColor: thirdColor,
//                         padding: EdgeInsets.symmetric(vertical: 1.5.h),
//                         shape: RoundedRectangleBorder(
//                           borderRadius: BorderRadius.circular(12),
//                         ),
//                       ),
//                       onPressed: () async {
//                         final isValid = _formKey.currentState!.validate();
//                         if (!isValid) return;
//                         _formKey.currentState!.save();
//                         Map<String, String> _payloadData = {
//                           'about_me': _aboutMe
//                         };
//                         Provider.of<DatabaseProvider>(context, listen: false)
//                             .updateUserInfo(userInfoAboutMeRoute, _payloadData);
//                         Navigator.pop(context);
//                       },
//                       child: Text(
//                         LocaleKeys.Common_Submit.tr(),
//                         style: TextStyle(
//                           color: mainColor,
//                           fontSize: 14.sp,
//                           fontWeight: FontWeight.w600,
//                         ),
//                       ),
//                     ),
//                   ),
//                   SizedBox(height: 2.h),
//                 ],
//               ),
//             ),
//           );
//         },
//       );
//     },
//   );
// }
