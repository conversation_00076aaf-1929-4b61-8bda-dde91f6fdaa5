import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:zupid/common_things/images.dart';
import 'package:zupid/common_things/my_fucntions.dart';
import 'package:zupid/translations/locale_keys.g.dart';
import 'package:zupid/widgets/loading_widget.dart';
import 'package:provider/provider.dart';
import 'package:zupid/providers/database_provider.dart';
import 'package:sizer/sizer.dart';

import '../common_things/analytics.dart';
import '../common_things/my_colors.dart';
import '../providers/auth_provider.dart';

class LocationPermissionScreen extends StatefulWidget {
  static const routeName = '/location-permission-screen';
  //
  @override
  State<LocationPermissionScreen> createState() =>
      _LocationPermissionScreenState();
}

class _LocationPermissionScreenState extends State<LocationPermissionScreen> {
  /////
  bool _isAnalytics = true;
  late final MyAnalytics myAnalytics;
  //////
  bool _isLoading = false;
  //
  @override
  Widget build(BuildContext context) {
    //////////
    if (_isAnalytics) {
      myAnalytics = Provider.of<MyAnalytics>(context, listen: false);
      myAnalytics.setLogEvent('LocationPremissionScreen', {});
      _isAnalytics = false;
    }
    ////////////
    return Scaffold(
      body: _isLoading
          ? LoadingWidget()
          : Container(
              height: 100.h,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    mainColor,
                    mainContrastColor.withOpacity(0.1),
                  ],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                ),
              ),
              child: SafeArea(
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 5.w),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      // Modern location icon with animation effect
                      Container(
                        width: 30.w,
                        height: 30.w,
                        decoration: BoxDecoration(
                          color: mainColor.withOpacity(0.8),
                          shape: BoxShape.circle,
                          boxShadow: [
                            BoxShadow(
                              color: mainContrastColor.withOpacity(0.3),
                              blurRadius: 15,
                              spreadRadius: 5,
                            ),
                          ],
                        ),
                        child: Icon(
                          Icons.location_on_rounded,
                          size: 20.w,
                          color: mainContrastColor,
                        ),
                      ),
                      SizedBox(height: 6.h),
                      // Title
                      Text(
                        "Enable Location",
                        style: TextStyle(
                          fontSize: 18.sp,
                          fontWeight: FontWeight.bold,
                          color: mainContrastColor,
                        ),
                      ),
                      SizedBox(height: 2.h),
                      // Description
                      Text(
                        "We need your location to find matches near you",
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: mainContrastColor.withOpacity(0.7),
                        ),
                      ),
                      SizedBox(height: 6.h),
                      // Button
                      Center(
                        child: Container(
                          width: 70.w,
                          child: ElevatedButton.icon(
                            style: ButtonStyle(
                              backgroundColor:
                                  MaterialStateProperty.all(thirdColor),
                              foregroundColor:
                                  MaterialStateProperty.all(mainColor),
                              padding: MaterialStateProperty.all(
                                EdgeInsets.symmetric(vertical: 1.5.h),
                              ),
                              shape: MaterialStateProperty.all(
                                RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(30),
                                ),
                              ),
                            ),
                            onPressed: () async {
                              bool serviceEnabled;
                              LocationPermission permission;

                              serviceEnabled =
                                  await Geolocator.isLocationServiceEnabled();

                              if (!serviceEnabled) {
                                callToast(
                                    "Please enable location services for the device.");
                                return;
                              }

                              permission = await Geolocator.requestPermission();

                              if (permission == LocationPermission.denied) {
                                permission =
                                    await Geolocator.requestPermission();
                                if (permission == LocationPermission.denied) {
                                  callToast(
                                      "Go to App Info -> Permissions -> Location.\n\nAnd select \n\n'Allow only while using the app'");
                                  return;
                                }
                              }

                              if (permission ==
                                  LocationPermission.deniedForever) {
                                callToast(
                                    "Go to App Info -> Permissions -> Location.\n\nAnd select \n\n'Allow only while using the app'");
                                return;
                              }

                              setState(() {
                                _isLoading = true;
                              });

                              await Provider.of<AuthProvider>(context,
                                      listen: false)
                                  .findLocation();

                              await Provider.of<AuthProvider>(context,
                                      listen: false)
                                  .updateCurrentLocation();

                              setState(() {
                                _isLoading = false;
                              });
                            },
                            icon: const Icon(Icons.location_on_rounded),
                            label: Text(
                              LocaleKeys.Common_Allow_location_access.tr(),
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 12.sp,
                              ),
                            ),
                          ),
                        ),
                      ),
                      SizedBox(height: 8.h),
                      // Tips in a card
                      Card(
                        color: mainColor.withOpacity(0.8),
                        elevation: 2,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(15),
                        ),
                        child: Padding(
                          padding: EdgeInsets.all(4.w),
                          child: Column(
                            children: [
                              Row(
                                children: [
                                  Icon(Icons.info_outline,
                                      color: thirdColor, size: 5.w),
                                  SizedBox(width: 2.w),
                                  Text(
                                    "Tips",
                                    style: TextStyle(
                                      fontSize: 12.sp,
                                      fontWeight: FontWeight.bold,
                                      color: mainContrastColor,
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(height: 2.h),
                              _buildTipItem(
                                  "Make sure your device location is turned on"),
                              SizedBox(height: 1.h),
                              _buildTipItem(
                                  "Check app permission and location permission"),
                              SizedBox(height: 1.h),
                              _buildTipItem(
                                  "Go to App Info and Delete Data, if facing any issue"),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
    );
  }

  Widget _buildTipItem(String text) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "•",
          style: TextStyle(
            fontSize: 12.sp,
            color: mainContrastColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(width: 2.w),
        Expanded(
          child: Text(
            text,
            style: TextStyle(
              fontSize: 10.sp,
              color: mainContrastColor.withOpacity(0.8),
            ),
          ),
        ),
      ],
    );
  }
}
