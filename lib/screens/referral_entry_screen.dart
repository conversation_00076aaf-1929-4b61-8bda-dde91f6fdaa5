import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:zupid/common_things/images.dart';
import 'package:zupid/common_things/my_colors.dart';
import 'package:zupid/common_things/my_fucntions.dart';
import 'package:zupid/providers/auth_provider.dart';
import 'package:zupid/providers/database_provider.dart';
import 'package:zupid/translations/locale_keys.g.dart';
import 'package:provider/provider.dart';
import 'package:sizer/sizer.dart';
import 'package:zupid/widgets/loading_widget.dart';

import '../common_things/analytics.dart';

class ReferralEntryScreen extends StatefulWidget {
  static const routeName = '/referral-entry-screen';

  @override
  State<ReferralEntryScreen> createState() => _ReferralEntryScreenState();
}

class _ReferralEntryScreenState extends State<ReferralEntryScreen> {
  /////
  bool _isAnalytics = true;
  late final MyAnalytics myAnalytics;
  //////
  bool _isLoading = false;
  final _formKey = GlobalKey<FormState>();
  String promoCode = "";

  @override
  Widget build(BuildContext context) {
    //////////
    if (_isAnalytics) {
      myAnalytics = Provider.of<MyAnalytics>(context, listen: false);
      myAnalytics.setLogEvent('ReferralEntryScreen', {});
      _isAnalytics = false;
    }
    int coinsPerReferral =
        Provider.of<AuthProvider>(context, listen: false).getCoinsPerReferral;
    ////////////
    return Scaffold(
      body: _isLoading
          ? LoadingWidget()
          : SingleChildScrollView(
              child: Container(
                height: 100.h,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      mainColor,
                      mainContrastColor.withOpacity(0.1),
                    ],
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                  ),
                ),
                child: SafeArea(
                  child: Padding(
                    padding:
                        EdgeInsets.symmetric(horizontal: 5.w, vertical: 5.h),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        // Icon in circular container
                        Container(
                          width: 25.w,
                          height: 25.w,
                          decoration: BoxDecoration(
                            color: mainColor.withOpacity(0.8),
                            shape: BoxShape.circle,
                            boxShadow: [
                              BoxShadow(
                                color: thirdColor.withOpacity(0.3),
                                blurRadius: 15,
                                spreadRadius: 5,
                              ),
                            ],
                          ),
                          child: Image.asset(
                            MyImages.kReferral,
                            width: 15.w,
                          ),
                        ),
                        SizedBox(height: 4.h),
                        Text(
                          'Have Referral Code?',
                          style: TextStyle(
                            fontSize: 18.sp,
                            fontWeight: FontWeight.bold,
                            color: mainContrastColor,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        SizedBox(height: 2.h),
                        Text(
                          "Enter a referral code to get $coinsPerReferral coin",
                          style: TextStyle(
                            fontSize: 12.sp,
                            color: mainContrastColor.withOpacity(0.8),
                          ),
                          textAlign: TextAlign.center,
                        ),
                        SizedBox(height: 5.h),
                        // Referral code card
                        Card(
                          color: Colors.transparent,
                          elevation: 2,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(15),
                          ),
                          child: Padding(
                            padding: EdgeInsets.all(4.w),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    Icon(Icons.card_giftcard,
                                        color: mainContrastColor, size: 6.w),
                                    SizedBox(width: 3.w),
                                    Text(
                                      "Referral Code",
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        color: mainContrastColor,
                                        fontSize: 14.sp,
                                      ),
                                    ),
                                  ],
                                ),
                                SizedBox(height: 2.h),
                                Form(
                                  key: _formKey,
                                  child: TextFormField(
                                    initialValue: "",
                                    style:
                                        TextStyle(fontWeight: FontWeight.bold),
                                    decoration: InputDecoration(
                                      filled: true,
                                      fillColor: mainColor,
                                      contentPadding: EdgeInsets.symmetric(
                                          vertical: 2.h, horizontal: 3.w),
                                      hintText: "Enter code",
                                      hintStyle: TextStyle(
                                          color: Colors.grey, fontSize: 12.sp),
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(10),
                                        borderSide: BorderSide(
                                            color:
                                                Colors.grey.withOpacity(0.3)),
                                      ),
                                      enabledBorder: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(10),
                                        borderSide: BorderSide(
                                            color:
                                                Colors.grey.withOpacity(0.3)),
                                      ),
                                      focusedBorder: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(10),
                                        borderSide: BorderSide(
                                            color: thirdColor, width: 2),
                                      ),
                                      errorBorder: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(10),
                                        borderSide: BorderSide(
                                            color: Colors.redAccent,
                                            width: 1.5),
                                      ),
                                    ),
                                    textInputAction: TextInputAction.next,
                                    inputFormatters: <TextInputFormatter>[
                                      LengthLimitingTextInputFormatter(7),
                                    ],
                                    validator: (value) {
                                      if (value == null ||
                                          containsOnlySpaces(value)) {
                                        return "Enter code";
                                      }
                                      return null;
                                    },
                                    onSaved: (value) {
                                      promoCode = value!;
                                    },
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                        SizedBox(height: 4.h),
                        // Buttons
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Container(
                              width: 40.w,
                              child: ElevatedButton(
                                style: ButtonStyle(
                                  backgroundColor:
                                      MaterialStateProperty.all(thirdColor),
                                  padding: MaterialStateProperty.all(
                                    EdgeInsets.symmetric(vertical: 1.5.h),
                                  ),
                                  shape: MaterialStateProperty.all(
                                    RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(30),
                                    ),
                                  ),
                                ),
                                onPressed: () async {
                                  final isValid =
                                      _formKey.currentState!.validate();
                                  if (!isValid) {
                                    return;
                                  }
                                  _formKey.currentState!.save();
                                  setState(() {
                                    _isLoading = true;
                                  });
                                  final result =
                                      await Provider.of<DatabaseProvider>(
                                              context,
                                              listen: false)
                                          .referral(promoCode);
                                  setState(() {
                                    _isLoading = false;
                                  });
                                  if (result) {
                                    callToast(
                                        "Congrats, $coinsPerReferral gold coins added!");
                                  } else {
                                    callToast("Invalid Code. Try again.");
                                  }
                                },
                                child: Text(
                                  LocaleKeys.Common_Submit.tr(),
                                  style: TextStyle(
                                    color: mainColor,
                                    fontWeight: FontWeight.bold,
                                    fontSize: 12.sp,
                                  ),
                                ),
                              ),
                            ),
                            SizedBox(width: 4.w),
                            Container(
                              width: 40.w,
                              child: ElevatedButton(
                                style: ButtonStyle(
                                  backgroundColor: MaterialStateProperty.all(
                                      Colors.grey.withOpacity(0.7)),
                                  padding: MaterialStateProperty.all(
                                    EdgeInsets.symmetric(vertical: 1.5.h),
                                  ),
                                  shape: MaterialStateProperty.all(
                                    RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(30),
                                    ),
                                  ),
                                ),
                                onPressed: () async {
                                  setState(() {
                                    _isLoading = true;
                                  });
                                  await Provider.of<DatabaseProvider>(context,
                                          listen: false)
                                      .referral("-1");
                                  setState(() {
                                    _isLoading = false;
                                  });
                                },
                                child: Text(
                                  "Skip",
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                    fontSize: 12.sp,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
    );
  }
}
