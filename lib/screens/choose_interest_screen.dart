import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:zupid/common_things/my_constants.dart';
import 'package:zupid/models/myInterest.dart';
import 'package:zupid/translations/locale_keys.g.dart';
import 'package:provider/provider.dart';
import 'package:sizer/sizer.dart';
import 'package:zupid/common_things/my_colors.dart';
import 'package:zupid/common_things/my_fucntions.dart';
import 'package:zupid/providers/database_provider.dart';
import 'package:zupid/widgets/loading_widget.dart';
import '../common_things/analytics.dart';

class ChooseInterestScreen extends StatefulWidget {
  static const routeName = '/choose-interest-screen';

  @override
  State<ChooseInterestScreen> createState() => _ChooseInterestScreenState();
}

class _ChooseInterestScreenState extends State<ChooseInterestScreen> {
  bool _isAnalytics = true;
  late final MyAnalytics myAnalytics;
  int maximumAllowed = 20;
  bool _isLoading = false;
  late List<String> _allCategories;
  late List<MyInterest> _allInterests;
  List<String> _selectedInterests = [];
  String? _selectedCategory;

  @override
  void initState() {
    super.initState();
    if (_isAnalytics) {
      myAnalytics = Provider.of<MyAnalytics>(context, listen: false);
      myAnalytics.setLogEvent('ChooseInterestScreen', {});
      _isAnalytics = false;
    }
    _fetchInterests();
  }

  void _fetchInterests() {
    final databaseProvider =
        Provider.of<DatabaseProvider>(context, listen: false);
    _allInterests = databaseProvider.getInterestList;
    _allCategories = findAllInterestsCategory(_allInterests);
    _selectedInterests = databaseProvider.getMyUserInfo.allInterests.interests;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.white,
        iconTheme: IconThemeData(color: mainColor),
        title: Text(
          '${LocaleKeys.ChooseInterestScreen_Select_interests.tr()} ($maximumAllowed ${LocaleKeys.ChooseInterestScreen_allowed.tr()})',
          style: TextStyle(
              fontSize: 14.sp, color: mainColor, fontWeight: FontWeight.bold),
        ),
      ),
      body: WillPopScope(
        onWillPop: () async {
          setState(() => _isLoading = true);
          Provider.of<DatabaseProvider>(context, listen: false)
              .EditInterests(_selectedInterests);
          setState(() => _isLoading = false);
          return true;
        },
        child: _isLoading
            ? LoadingWidget()
            : Padding(
                padding: EdgeInsets.symmetric(vertical: 2.h, horizontal: 4.w),
                child: _selectedCategory == null
                    ? GridView.builder(
                        itemCount: _allCategories.length,
                        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 2,
                          childAspectRatio: 1.5,
                          mainAxisSpacing: 2.h,
                          crossAxisSpacing: 2.w,
                        ),
                        itemBuilder: (context, index) {
                          MyInterest categoryInterest =
                              _allInterests.firstWhere(
                            (interest) =>
                                interest.category == _allCategories[index],
                          );

                          return GestureDetector(
                            onTap: () {
                              setState(() {
                                _selectedCategory = _allCategories[index];
                              });
                            },
                            child: Container(
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(2.h),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black12,
                                    blurRadius: 6,
                                    offset: Offset(0, 3),
                                  ),
                                ],
                              ),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  CachedNetworkImage(
                                    imageUrl: interestR2Folder +
                                        _allCategories[index] +
                                        '.png',
                                    width: 50,
                                    height: 50,
                                    fit: BoxFit.cover,
                                    placeholder: (context, url) =>
                                        LoadingWidget(),
                                    errorWidget: (context, url, error) =>
                                        Icon(Icons.error, color: Colors.grey),
                                  ),
                                  SizedBox(height: 1.h),
                                  Text(
                                    _allCategories[index],
                                    style: TextStyle(
                                      color: Colors.black87,
                                      fontSize: 12.sp,
                                      fontWeight: FontWeight.bold,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ],
                              ),
                            ),
                          );
                        },
                      )
                    : Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          TextButton.icon(
                            onPressed: () {
                              setState(() {
                                _selectedCategory = null;
                              });
                            },
                            icon: Icon(Icons.arrow_back, color: mainColor),
                            label: Text("Go Back"),
                          ),
                          Expanded(
                            child: GridView.builder(
                              itemCount: _allInterests
                                  .where((interest) =>
                                      interest.category == _selectedCategory)
                                  .length,
                              gridDelegate:
                                  SliverGridDelegateWithFixedCrossAxisCount(
                                crossAxisCount: 3,
                                childAspectRatio: 1,
                                mainAxisSpacing: 1.h,
                                crossAxisSpacing: 1.w,
                              ),
                              itemBuilder: (context, index) {
                                final interest = _allInterests
                                    .where((interest) =>
                                        interest.category == _selectedCategory)
                                    .toList()[index];
                                return GestureDetector(
                                  onTap: () {
                                    setState(() {
                                      if (_selectedInterests
                                          .contains(interest.id)) {
                                        _selectedInterests.remove(interest.id);
                                      } else if (_selectedInterests.length <
                                          maximumAllowed) {
                                        _selectedInterests.add(interest.id);
                                      } else {
                                        callToast(
                                            '$maximumAllowed ${LocaleKeys.ChooseInterestScreen_allowed.tr()}');
                                      }
                                    });
                                  },
                                  child: Container(
                                    decoration: BoxDecoration(
                                      color: _selectedInterests
                                              .contains(interest.id)
                                          ? mainColor.withOpacity(0.1)
                                          : Colors.white,
                                      borderRadius: BorderRadius.circular(1.h),
                                      border: Border.all(
                                        color: _selectedInterests
                                                .contains(interest.id)
                                            ? mainColor
                                            : Colors.transparent,
                                        width: 1.5,
                                      ),
                                    ),
                                    child: Column(
                                      children: [
                                        Expanded(
                                          child: CachedNetworkImage(
                                            imageUrl: interestR2Folder +
                                                interest.image,
                                            fit: BoxFit.cover,
                                            placeholder: (context, url) =>
                                                LoadingWidget(),
                                            errorWidget:
                                                (context, url, error) => Icon(
                                                    Icons.error,
                                                    color: Colors.grey),
                                          ),
                                        ),
                                        Padding(
                                          padding: EdgeInsets.all(0.5.h),
                                          child: Text(
                                            interest.name,
                                            textAlign: TextAlign.center,
                                            style: TextStyle(
                                              fontSize: 8.sp,
                                              color: _selectedInterests
                                                      .contains(interest.id)
                                                  ? mainContrastColor
                                                  : Colors.black87,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                );
                              },
                            ),
                          ),
                        ],
                      ),
              ),
      ),
    );
  }
}
