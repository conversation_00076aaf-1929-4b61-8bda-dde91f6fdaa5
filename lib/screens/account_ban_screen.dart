import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:zupid/translations/locale_keys.g.dart';
import 'package:provider/provider.dart';
import 'package:sizer/sizer.dart';

import '../common_things/analytics.dart';

class AccountBanScreen extends StatelessWidget {
  static const routeName = '/account-ban-screen';
  bool _isAnalytics = true;
  late final MyAnalytics myAnalytics;

  @override
  Widget build(BuildContext context) {
    //analytics
    if (_isAnalytics) {
      myAnalytics = Provider.of<MyAnalytics>(context, listen: false);
      myAnalytics.setLogEvent('AccountBanScreen', {});
      _isAnalytics = false;
    }
    //
    return WillPopScope(
      onWillPop: () async => false,
      child: Scaffold(
        body: Container(
          height: 100.h,
          width: 100.h,
          color: Colors.white,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Icon(
                FontAwesomeIcons.ban,
                color: Colors.red[700],
                size: 5.h,
              ),
              SizedBox(height: 4.h),
              Text(
                LocaleKeys.Common_Your_account_is_banned.tr(),
                style: TextStyle(
                    fontSize: 2.h,
                    fontWeight: FontWeight.w500,
                    color: Colors.black),
              )
            ],
          ),
        ),
      ),
    );
  }
}
