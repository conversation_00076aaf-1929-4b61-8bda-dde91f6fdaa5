import 'dart:convert';
import 'dart:io';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:zupid/models/myUserInfo.dart';
import 'package:zupid/providers/database_provider.dart';
import 'package:zupid/screens/choose_language.dart';
import 'package:zupid/translations/locale_keys.g.dart';
import 'package:provider/provider.dart';
import 'package:sizer/sizer.dart';
import 'package:zupid/common_things/my_colors.dart';
import 'package:zupid/providers/auth_provider.dart';
import 'package:zupid/screens/webpage_screen.dart';
import 'package:zupid/widgets/loading_widget.dart';
import 'package:url_launcher/url_launcher.dart';

import '../common_things/analytics.dart';
import '../common_things/my_fucntions.dart';
import 'about_app_screen.dart'; // Ensure this import is present if AboutAppScreen is used.

// New screen for account actions
class AccountActionsScreen extends StatefulWidget {
  static const routeName = '/account-actions-screen';

  const AccountActionsScreen({super.key});

  @override
  State<AccountActionsScreen> createState() => _AccountActionsScreenState();
}

class _AccountActionsScreenState extends State<AccountActionsScreen> {
  bool _isLoading = false;
  /////
  bool _isAnalytics = true;
  late final MyAnalytics myAnalytics;

  /// Shows a confirmation dialog for hiding/unhiding the profile.
  Future<void> _showHideProfileDialog(BuildContext context) async {
    final myUserInfo =
        Provider.of<DatabaseProvider>(context, listen: false).getMyUserInfo;
    final bool isCurrentlyHidden = myUserInfo.isHide;

    return showDialog<void>(
      context: context,
      builder: (BuildContext dialogContext) {
        return Dialog(
          backgroundColor: Colors.black,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(24),
            side: const BorderSide(color: mainContrastColor, width: 2),
          ),
          elevation: 24,
          child: Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [Colors.black87, Colors.black],
              ),
              borderRadius: BorderRadius.circular(24),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      isCurrentlyHidden
                          ? Icons.visibility
                          : Icons.visibility_off,
                      color: mainContrastColor,
                      size: 28,
                    ),
                    const SizedBox(width: 12),
                    Text(
                      isCurrentlyHidden ? 'Show Profile?' : 'Hide Profile?',
                      style: const TextStyle(
                        color: mainContrastColor,
                        fontSize: 22,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 20),
                Text(
                  isCurrentlyHidden
                      ? 'Are you sure you want to make your profile visible to everyone?'
                      : 'Are you sure you want to hide your profile from everyone?',
                  style: const TextStyle(
                    color: Colors.white70,
                    fontSize: 14,
                    height: 1.4,
                  ),
                ),
                const SizedBox(height: 24),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    _buildActionButton(
                      text: 'CANCEL',
                      color: Colors.grey[800]!,
                      textColor: Colors.white,
                      onPressed: () => Navigator.of(dialogContext).pop(),
                    ),
                    _buildActionButton(
                      text: isCurrentlyHidden ? 'SHOW' : 'HIDE',
                      color: mainContrastColor,
                      textColor: Colors.white,
                      onPressed: () async {
                        Navigator.of(dialogContext).pop();
                        setState(() {
                          _isLoading = true;
                        });
                        await Provider.of<DatabaseProvider>(context,
                                listen: false)
                            .updateHideProfile();
                        setState(() {
                          _isLoading = false;
                        });
                        callToast(isCurrentlyHidden
                            ? "Your profile is now visible to everyone."
                            : "Your profile is now hidden from everyone.");
                      },
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// Shows a confirmation dialog for logging out.
  Future<void> _showLogoutDialog(BuildContext context) async {
    return showDialog<void>(
      context: context,
      builder: (BuildContext dialogContext) {
        return Dialog(
          backgroundColor: Colors.black,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(24),
            side: const BorderSide(color: mainContrastColor, width: 2),
          ),
          elevation: 24,
          child: Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [Colors.black87, Colors.black],
              ),
              borderRadius: BorderRadius.circular(24),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Row(
                  children: [
                    Icon(Icons.logout, color: mainContrastColor, size: 28),
                    SizedBox(width: 12),
                    Text(
                      'Log Out?',
                      style: TextStyle(
                        color: mainContrastColor,
                        fontSize: 22,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 20),
                const Text(
                  'Are you sure you want to log out of your account?',
                  style: TextStyle(
                    color: Colors.white70,
                    fontSize: 14,
                    height: 1.4,
                  ),
                ),
                const SizedBox(height: 24),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    _buildActionButton(
                      text: 'CANCEL',
                      color: Colors.grey[800]!,
                      textColor: Colors.white,
                      onPressed: () => Navigator.of(dialogContext).pop(),
                    ),
                    _buildActionButton(
                      text: 'LOG OUT',
                      color: mainContrastColor,
                      textColor: Colors.white,
                      onPressed: () async {
                        Navigator.of(dialogContext).pop();
                        setState(() {
                          _isLoading = true;
                        });
                        Provider.of<DatabaseProvider>(context, listen: false)
                            .logout();
                        await Provider.of<AuthProvider>(context, listen: false)
                            .logout();
                        setState(() {
                          _isLoading = false;
                        });
                        Navigator.of(context)
                            .popUntil((route) => route.isFirst);
                      },
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// Shows the delete account dialog with detailed information.
  void _showDeleteAccountDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return Dialog(
          backgroundColor: Colors.black,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(24),
            side: const BorderSide(color: mainContrastColor, width: 2),
          ),
          elevation: 24,
          child: Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [Colors.black87, Colors.black],
              ),
              borderRadius: BorderRadius.circular(24),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Row(
                  children: [
                    Icon(Icons.warning_rounded,
                        color: mainContrastColor, size: 28),
                    SizedBox(width: 12),
                    Text(
                      'Are you sure?',
                      style: TextStyle(
                        color: mainContrastColor,
                        fontSize: 22,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 20),
                _buildInfoRow(Icons.timer,
                    'Account will be deleted after 90 days of inactivity'),
                const SizedBox(height: 12),
                _buildInfoRow(Icons.refresh,
                    'You can reactivate by logging in during this period'),
                const SizedBox(height: 12),
                _buildInfoRow(Icons.visibility_off,
                    'Hide your profile instead using the option below'),
                const SizedBox(height: 24),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    _buildActionButton(
                      text: 'GO BACK',
                      color: Colors.grey[800]!,
                      textColor: Colors.white,
                      onPressed: () => Navigator.of(dialogContext).pop(),
                    ),
                    _buildActionButton(
                      text: 'HIDE PROFILE',
                      color: Colors.grey[800]!,
                      textColor: mainContrastColor,
                      onPressed: () async {
                        Navigator.of(dialogContext).pop();
                        setState(() {
                          _isLoading = true;
                        });
                        await Provider.of<DatabaseProvider>(context,
                                listen: false)
                            .updateHideProfileToTrue();
                        setState(() {
                          _isLoading = false;
                        });
                        callToast("Your profile is now hidden from everyone.");
                      },
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                _buildActionButton(
                  text: 'DELETE ACCOUNT',
                  color: Colors.red,
                  textColor: Colors.white,
                  isFullWidth: true,
                  onPressed: () async {
                    Navigator.of(dialogContext).pop();
                    setState(() {
                      _isLoading = true;
                    });
                    try {
                      await Provider.of<DatabaseProvider>(context,
                              listen: false)
                          .updateUserInfoIsDeleted(true);
                      await Provider.of<AuthProvider>(context, listen: false)
                          .logout();
                      setState(() {
                        _isLoading = false;
                      });
                      Navigator.of(context).popUntil((route) =>
                          route.isFirst); // Go back to the first screen
                    } catch (error) {
                      setState(() {
                        _isLoading = false;
                      });
                      callToast("Error deleting account. Please try again.");
                    }
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildInfoRow(IconData icon, String text) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(icon, color: mainContrastColor, size: 20),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            text,
            style: const TextStyle(
              color: Colors.white70,
              fontSize: 14,
              height: 1.4,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildActionButton({
    required String text,
    required Color color,
    required Color textColor,
    bool isFullWidth = false,
    required VoidCallback onPressed,
  }) {
    return SizedBox(
      width: isFullWidth ? double.infinity : null,
      child: ElevatedButton(
        style: ElevatedButton.styleFrom(
          backgroundColor: color,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        ),
        onPressed: onPressed,
        child: Text(
          text,
          style: TextStyle(
            color: textColor,
            fontWeight: FontWeight.bold,
            fontSize: 14,
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    //////////
    if (_isAnalytics) {
      myAnalytics = Provider.of<MyAnalytics>(context, listen: false);
      myAnalytics.setLogEvent('ManageAccountScreen', {});
      _isAnalytics = false;
    }
    ////////////

    MyUserInfo _myUserInfo =
        Provider.of<DatabaseProvider>(context).getMyUserInfo;

    // Define a common button width for consistency
    final double buttonWidth = 70.w; // 70% of screen width

    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        title: const Text(' Manage Account'),
      ),
      body: _isLoading
          ? LoadingWidget()
          : SingleChildScrollView(
              padding: EdgeInsets.all(4.w),
              child: Column(
                // mainAxisAlignment: MainAxisAlignment.center,
                // crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  SizedBox(height: 2.h),

                  /// Show/Hide Profile Button
                  Center(
                    child: SizedBox(
                      width: buttonWidth, // Apply fixed width
                      child: ElevatedButton.icon(
                        onPressed: () => _showHideProfileDialog(context),
                        icon: Icon(
                          _myUserInfo.isHide
                              ? Icons.visibility
                              : Icons.visibility_off,
                          color: mainColor,
                        ),
                        label: Text(
                          _myUserInfo.isHide
                              ? 'Show Your Profile'
                              : 'Hide Your Profile',
                          style: TextStyle(
                              fontSize: 12.sp,
                              color: mainColor,
                              fontWeight: FontWeight.w500),
                        ),
                        style: ButtonStyle(
                          padding: MaterialStateProperty.all(
                              EdgeInsets.symmetric(
                                  vertical:
                                      2.w)), // Consistent vertical padding
                          backgroundColor: MaterialStateProperty.all(
                              _myUserInfo.isHide
                                  ? mainContrastColor
                                  : Colors.grey),
                          shape:
                              MaterialStateProperty.all<RoundedRectangleBorder>(
                            RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(
                                  12), // Consistent border radius
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                  SizedBox(height: 2.h),

                  /// Log Out Button
                  Center(
                    child: SizedBox(
                      width: buttonWidth, // Apply fixed width
                      child: ElevatedButton.icon(
                        onPressed: () => _showLogoutDialog(context),
                        icon: const Icon(
                          Icons.logout,
                          color: mainColor,
                        ),
                        label: Text(
                          'Log Out',
                          style: TextStyle(
                              fontSize: 12.sp,
                              color: mainColor,
                              fontWeight: FontWeight.w500),
                        ),
                        style: ButtonStyle(
                          padding: MaterialStateProperty.all(
                              EdgeInsets.symmetric(
                                  vertical:
                                      2.w)), // Consistent vertical padding
                          backgroundColor:
                              MaterialStateProperty.all(thirdColor),
                          shape:
                              MaterialStateProperty.all<RoundedRectangleBorder>(
                            RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(
                                  12), // Consistent border radius
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                  SizedBox(height: 8.h),

                  /// Delete Account Button
                  Center(
                    child: SizedBox(
                      width: buttonWidth, // Apply fixed width
                      child: ElevatedButton.icon(
                        onPressed: () {
                          Provider.of<MyAnalytics>(context, listen: false)
                              .setLogEvent('DeleteAccountClick', {});
                          _showDeleteAccountDialog(context);
                        },
                        icon: const Icon(
                          Icons.delete,
                          color: mainColor,
                        ),
                        label: const Text(
                          'Delete Your Account',
                          style: TextStyle(
                              color: mainColor, fontWeight: FontWeight.w500),
                        ),
                        style: ButtonStyle(
                          padding: MaterialStateProperty.all(
                              EdgeInsets.symmetric(
                                  vertical:
                                      2.w)), // Consistent vertical padding
                          backgroundColor:
                              MaterialStateProperty.all(Colors.red),
                          shape:
                              MaterialStateProperty.all<RoundedRectangleBorder>(
                            RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(
                                  12), // Consistent border radius
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                  SizedBox(
                    height: 10.h,
                  ),
                  Text(
                    textAlign: TextAlign.center,
                    "For any issue or query",
                    style: TextStyle(
                      fontSize: 12.sp,
                      fontWeight: FontWeight.bold,
                      color: mainContrastColor,
                    ),
                  ),
                  SizedBox(height: 1.h),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // IconButton(
                      //   icon: const FaIcon(FontAwesomeIcons.telegram,
                      //       color: Colors.blue),
                      //   onPressed: () async {
                      //     const url = "https://t.me/zupid";
                      //     if (await canLaunch(url)) {
                      //       await launch(url);
                      //     } else {
                      //       callToast("Could not launch Instagram");
                      //     }
                      //   },
                      // ),
                      IconButton(
                        icon: const FaIcon(FontAwesomeIcons.instagram,
                            color: Color(0xFFE4405F)),
                        onPressed: () async {
                          myAnalytics.setLogEvent('Click_Instagram', {});
                          const url = "https://www.instagram.com/zupid_dating";
                          if (await canLaunch(url)) {
                            await launch(url);
                          } else {
                            callToast("Could not launch Instagram");
                          }
                        },
                      ),
                      IconButton(
                        icon: const FaIcon(FontAwesomeIcons.twitter,
                            color: Color(0xFF1DA1F2)),
                        onPressed: () async {
                          myAnalytics.setLogEvent('Click_Twitter', {});

                          const url = "https://www.twitter.com/zupid_dating";
                          if (await canLaunch(url)) {
                            await launch(url);
                          } else {
                            callToast("Could not launch Twitter");
                          }
                        },
                      ),
                      IconButton(
                        icon: const FaIcon(FontAwesomeIcons.youtube,
                            color: Color(0xFFFF0000)),
                        onPressed: () async {
                          myAnalytics.setLogEvent('Click_YouTube', {});

                          const url = "https://www.youtube.com/@zupid_dating";
                          if (await canLaunch(url)) {
                            await launch(url);
                          } else {
                            callToast("Could not launch YouTube");
                          }
                        },
                      ),
                    ],
                  ),
                ],
              ),
            ),
    );
  }
}
