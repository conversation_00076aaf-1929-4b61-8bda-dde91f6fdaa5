import 'package:auto_size_text/auto_size_text.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:zupid/common_things/my_colors.dart';
import 'package:zupid/common_things/my_constants.dart';
import 'package:zupid/common_things/my_fucntions.dart';
import 'package:zupid/models/myPersonalityTraits.dart';
import 'package:zupid/models/myPromptList.dart';
import 'package:zupid/models/myUserInfo.dart';
import 'package:zupid/providers/database_provider.dart';
import 'package:zupid/screens/choose_prompt_screen.dart';
import 'package:zupid/translations/locale_keys.g.dart';
import 'package:zupid/widgets/each_container_item.dart';
import 'package:provider/provider.dart';
import 'package:sizer/sizer.dart';

class PersonalityTab extends StatefulWidget {
  // final List<MyPersonalityTraits>
  //     allPersonalityTraits; // Add this to pass all traits
  final MyUserInfo userInfo;
  final bool isEditActive;

  PersonalityTab({
    // required this.allPersonalityTraits,
    required this.userInfo,
    required this.isEditActive,
  });

  @override
  _PersonalityTabState createState() => _PersonalityTabState();
}

class _PersonalityTabState extends State<PersonalityTab> {
  bool runOnce = true;
  MyUserInfo? myUserInfo;

  // This will hold the list with updated values for each trait
  List<Map<String, dynamic>> _userPersonalityTraits = [];

  @override
  void initState() {
    super.initState();
  }

  void _initializePersonalityTraits() {
    List<Map<String, dynamic>> traitsList = [];
    final allPersonalityTraits =
        Provider.of<DatabaseProvider>(context, listen: false)
            .getAllPersonalityTraitsList;
    for (var trait in allPersonalityTraits) {
      // Check if the user already has a value for this trait
      var userTrait = myUserInfo!.allInterests.personalityTraitsJson.firstWhere(
          (userTrait) => userTrait.traitId == trait.id,
          orElse: () => PersonalityTraitsJson(traitId: trait.id, value: 0));

      // Add the trait with either user's value or default to the list
      traitsList.add({
        'id': userTrait.traitId,
        'category': trait.category,
        'name': trait.name,
        'value': userTrait.value.toDouble(),
        'min': trait.min,
        'max': trait.max,
      });
    }

    setState(() {
      _userPersonalityTraits = traitsList;
    });
  }

  @override
  Widget build(BuildContext context) {
    myUserInfo = widget.userInfo;
    // myUserInfo =
    //     Provider.of<DatabaseProvider>(context, listen: true).getMyUserInfo;
    if (runOnce) {
      _initializePersonalityTraits();
      runOnce = false;
    }
    final _promptList =
        Provider.of<DatabaseProvider>(context, listen: false).getPromptList;
    return Padding(
      padding: EdgeInsets.all(8.0),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // about me //
            SizedBox(height: 2.h),
            if (!(widget.isEditActive == false && myUserInfo!.aboutMe == ''))
              GestureDetector(
                onTap: () async {
                  if (!widget.isEditActive) {
                    return;
                  }
                  await _editAboutMe(context, myUserInfo!.aboutMe);
                },
                child: EachInfoContainer(
                  title: 'About Myself',
                  varWidget: Text(
                    myUserInfo!.aboutMe == ''
                        ? '\n\n${LocaleKeys.EditInfoScreen_Please_enter_your_life_story.tr()}..\n\n'
                        : myUserInfo!.aboutMe,
                    style: TextStyle(
                      color: thirdColor.withOpacity(0.8),
                      fontWeight: FontWeight.bold,
                      fontSize: 12.sp,
                    ),
                  ),
                ),
              ),
            SizedBox(
              height: 2.h,
            ),
            // prompts //
            myUserInfo!.allInterests.prompts.length >= 1
                ? PromptQuestionAnswerWidget(
                    promptList: _promptList,
                    promptNumber: 1,
                    isEditActive: widget.isEditActive,
                    varUserInfo: myUserInfo!,
                  )
                : widget.isEditActive
                    ? AddPromptWidget(promptNumber: 1)
                    : Container(),
            myUserInfo!.allInterests.prompts.length >= 2
                ? PromptQuestionAnswerWidget(
                    promptList: _promptList,
                    promptNumber: 2,
                    isEditActive: widget.isEditActive,
                    varUserInfo: myUserInfo!,
                  )
                : widget.isEditActive
                    ? AddPromptWidget(promptNumber: 2)
                    : Container(),
            myUserInfo!.allInterests.prompts.length >= 3
                ? PromptQuestionAnswerWidget(
                    promptList: _promptList,
                    promptNumber: 3,
                    isEditActive: widget.isEditActive,
                    varUserInfo: myUserInfo!,
                  )
                : widget.isEditActive
                    ? AddPromptWidget(promptNumber: 3)
                    : Container(),
            SizedBox(height: 2.h),
            for (var category in _getCategories(_userPersonalityTraits))
              Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  SizedBox(height: 1.h),
                  Text(
                    category,
                    style:
                        TextStyle(fontSize: 14.sp, fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 1.h),
                  Column(
                    children: _userPersonalityTraits
                        .where((trait) => trait['category'] == category)
                        .map((trait) {
                      return Padding(
                        padding: EdgeInsets.symmetric(
                            vertical: 1.h, horizontal: 2.w),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Stack(
                              alignment: Alignment.center, // Center the stars
                              children: [
                                // Left Text (Min)
                                Align(
                                  alignment: Alignment
                                      .centerLeft, // Align left text to the left
                                  child: Text(
                                    trait['min'],
                                    style: TextStyle(
                                      fontSize: 12.sp,
                                      color: trait['value'] < 3 &&
                                              trait['value'] > 0
                                          ? thirdColor
                                          : Colors.grey,
                                      fontWeight: trait['value'] < 3 &&
                                              trait['value'] > 0
                                          ? FontWeight.bold
                                          : FontWeight.normal,
                                    ),
                                  ),
                                ),

                                // Star Rating (1-5)
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: List.generate(5, (index) {
                                    return GestureDetector(
                                      onTap: widget.isEditActive
                                          ? () {
                                              setState(() {
                                                trait['value'] = index + 1;
                                              });
                                              Provider.of<DatabaseProvider>(
                                                      context,
                                                      listen: false)
                                                  .UpdatePersonalityTraitValue(
                                                      trait['id'], index + 1);
                                            }
                                          : null,
                                      child: Icon(
                                        index < trait['value']
                                            ? Icons.star
                                            : Icons.star_border,
                                        color: index < trait['value']
                                            ? thirdColor
                                            : Colors.grey,
                                        size: 20.sp,
                                      ),
                                    );
                                  }),
                                ),

                                // Right Text (Max)
                                Align(
                                  alignment: Alignment
                                      .centerRight, // Align right text to the right
                                  child: Text(
                                    trait['max'],
                                    style: TextStyle(
                                      fontSize: 12.sp,
                                      color: trait['value'] > 3
                                          ? thirdColor
                                          : Colors.grey,
                                      fontWeight: trait['value'] > 3
                                          ? FontWeight.bold
                                          : FontWeight.normal,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            // SliderTheme(
                            //   data: SliderThemeData(
                            //     activeTrackColor: thirdColor,
                            //     inactiveTrackColor: Colors.grey.shade300,
                            //     thumbColor: thirdColor,
                            //     overlayColor: thirdColor.withAlpha(32),
                            //     trackHeight: 0.5.h,
                            //     thumbShape: RoundSliderThumbShape(
                            //         enabledThumbRadius: 1.h),
                            //   ),
                            //   child: IgnorePointer(
                            //     ignoring: !widget.isEditActive,
                            //     child: Slider(
                            //       value: trait['value'],
                            //       min: 1,
                            //       max: 5,
                            //       divisions: 4,
                            //       // label: trait['name'],
                            //       onChanged: (value) {
                            //         setState(() {
                            //           trait['value'] = value;
                            //         });
                            //       },
                            //       onChangeEnd: (value) {
                            //         Provider.of<DatabaseProvider>(context,
                            //                 listen: false)
                            //             .UpdatePersonalityTraitValue(
                            //                 trait['id'], value.toInt());

                            //         // Here you can add code to update the backend when a change occurs
                            //       },
                            //     ),
                            //   ),
                            // ),
                          ],
                        ),
                      );
                    }).toList(),
                  ),
                  SizedBox(
                    height: 8.h,
                  ),
                ],
              ),
          ],
        ),
      ),
    );
  }

  // Helper function to get unique categories
  List<dynamic> _getCategories(List<Map<String, dynamic>> traits) {
    return traits.map((trait) => trait['category']).toSet().toList();
  }
}

Future<void> _editAboutMe(context, String _aboutMe) async {
  final _formKey = GlobalKey<FormState>();
  if (_aboutMe == '') {
    _aboutMe = '';
  }
  return showModalBottomSheet(
      isScrollControlled: true,
      context: context,
      builder: (context) {
        return StatefulBuilder(
            builder: (BuildContext context, StateSetter mystate) {
          return Container(
            color: mainColor,
            child: SingleChildScrollView(
              child: Wrap(
                children: [
                  Padding(
                    padding: MediaQuery.of(context).viewInsets,
                    child: Container(
                      child: Padding(
                        padding: EdgeInsets.all(2.h),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                          children: [
                            SizedBox(
                              height: 5.h,
                            ),
                            // if (answer == 0)
                            Form(
                              key: _formKey,
                              child: TextFormField(
                                maxLines: 4,
                                initialValue: _aboutMe,
                                enableSuggestions: false,
                                autocorrect: false,
                                decoration: InputDecoration(
                                  labelText: 'About Myself',
                                  labelStyle: TextStyle(
                                    color:
                                        mainContrastColor, // Use your main theme color
                                    fontWeight: FontWeight.bold,
                                  ),
                                  filled: true,
                                  fillColor:
                                      mainColor, // Light background color
                                  border: OutlineInputBorder(
                                    borderSide: BorderSide(
                                        color: mainContrastColor, width: 1.5),
                                    borderRadius: BorderRadius.circular(
                                        10), // Rounded borders
                                  ),
                                  focusedBorder: OutlineInputBorder(
                                    borderSide: BorderSide(
                                        color: mainContrastColor, width: 2),
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                  enabledBorder: OutlineInputBorder(
                                    borderSide: BorderSide(
                                        color: mainContrastColor, width: 1.5),
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                  errorBorder: OutlineInputBorder(
                                    borderSide: BorderSide(
                                        color: thirdColor, width: 1.5),
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                  contentPadding: EdgeInsets.symmetric(
                                      vertical: 16, horizontal: 16),
                                  errorMaxLines: 2,
                                ),
                                textInputAction: TextInputAction.next,
                                inputFormatters: <TextInputFormatter>[
                                  LengthLimitingTextInputFormatter(256),
                                ],
                                validator: (value) {
                                  if (value == null ||
                                      containsOnlySpaces(value)) {
                                    return LocaleKeys
                                            .EditInfoScreen_Please_enter_your_life_story
                                        .tr();
                                  }
                                },
                                onSaved: (value) {
                                  _aboutMe = value!;
                                },
                              ),
                            ),
                            Padding(
                              padding: EdgeInsets.all(5.sp),
                              child: ElevatedButton(
                                style: const ButtonStyle(
                                    backgroundColor:
                                        MaterialStatePropertyAll(thirdColor)),
                                onPressed: () async {
                                  final isValid =
                                      _formKey.currentState!.validate();
                                  if (!isValid) {
                                    return;
                                  }
                                  _formKey.currentState!.save();
                                  Map<String, String> _payloadData = {
                                    'about_me': _aboutMe
                                  };
                                  Provider.of<DatabaseProvider>(context,
                                          listen: false)
                                      .updateUserInfo(
                                          userInfoAboutMeRoute, _payloadData);

                                  Navigator.pop(context);
                                },
                                child: Text(
                                  LocaleKeys.Common_Submit.tr(),
                                  style: const TextStyle(color: mainColor),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        });
      });
}
