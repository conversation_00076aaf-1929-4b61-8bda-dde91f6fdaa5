import 'package:blur/blur.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:zupid/screens/buy_premium_cashfree.dart';
import 'package:zupid/translations/locale_keys.g.dart';
import 'package:sizer/sizer.dart';

import 'package:zupid/common_things/my_colors.dart';
import 'package:zupid/screens/buy_premium_screen.dart';

class MyBlurWidget extends StatelessWidget {
  final double padding;
  final bool isMembership;
  final double myBlur;

  final Widget applyOnWidget;
  const MyBlurWidget({
    Key? key,
    required this.padding,
    required this.isMembership,
    required this.myBlur,
    required this.applyOnWidget,
  }) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return isMembership
        ? applyOnWidget
        : Blur(
            blur: myBlur,
            // blurColor: mainColor,

            colorOpacity: .2,
            blurColor: mainColor,
            overlay: Center(
              child: ElevatedButton(
                style: ButtonStyle(
                    padding: MaterialStateProperty.all(
                      EdgeInsets.symmetric(vertical: 1.5.h, horizontal: 10.w),
                    ),
                    shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                      RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(3.h),
                      ),
                    ),
                    backgroundColor: MaterialStatePropertyAll(mainColor)),
                onPressed: () {
                  Navigator.of(context)
                      .pushNamed(BuyPremiumCashfreeScreen.routeName);
                },
                child: Text(
                  LocaleKeys.PremiumFeatures_Buy_Premium_to_Unlock.tr(),
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 11.sp,
                  ),
                ),
              ),
            ),
            child: Padding(
              padding: EdgeInsets.fromLTRB(0, padding.h, 0, padding.h),
              child: applyOnWidget,
            ),
          );
  }
}
