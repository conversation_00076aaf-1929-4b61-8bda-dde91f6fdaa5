import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:zupid/common_things/my_colors.dart';
import 'package:zupid/common_things/my_constants.dart';
import 'package:zupid/models/myInterest.dart';
import 'package:zupid/models/myUserInfo.dart';
import 'package:zupid/providers/database_provider.dart';
import 'package:zupid/screens/edit_movie_screen.dart';
import 'package:zupid/screens/select_category_interest_screen.dart';
import 'package:zupid/widgets/loading_widget.dart';
import 'package:zupid/widgets/movie_card_widget.dart';
import 'package:provider/provider.dart';
import 'package:sizer/sizer.dart';

class InterestTab extends StatelessWidget {
  final MyUserInfo myUserInfo;
  final bool isEditActive;
  InterestTab({required this.myUserInfo, required this.isEditActive});
  //
  /// interests ////
  // int maximumAllowed = 20;
  // late List<String> _allCategories;
  late List<MyInterest> _allInterests;
  List<String> _selectedInterests = [];

  // String? _selectedCategory;
  //
  void _fetchInterests(DatabaseProvider databaseProvider) {
    _allInterests = databaseProvider.getInterestList;
    // _allCategories = findAllInterestsCategory(_allInterests);
    _selectedInterests = myUserInfo.allInterests.interests;
  }

  @override
  Widget build(BuildContext context) {
    final databaseProvider =
        Provider.of<DatabaseProvider>(context, listen: false);
    _fetchInterests(databaseProvider);

    //
    return Padding(
      padding: EdgeInsets.all(2.w),
      child: SingleChildScrollView(
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  "Favourite Movies & TV Shows",
                  style:
                      TextStyle(fontSize: 14.sp, fontWeight: FontWeight.bold),
                ),
                SizedBox(
                  width: 2.w,
                ),
                if (isEditActive)
                  IconButton(
                    icon: const Icon(
                      Icons.edit,
                      color: thirdColor,
                    ),
                    onPressed: () {
                      Navigator.of(context)
                          .pushNamed(EditMovieScreen.routeName);
                    },
                  ),
              ],
            ),
            SizedBox(height: 1.h),
            myUserInfo.allInterests.movies.isNotEmpty
                ? GridView.count(
                    shrinkWrap: true,
                    physics: NeverScrollableScrollPhysics(),
                    crossAxisCount: 3,
                    childAspectRatio: .7,
                    children: myUserInfo.allInterests.movies.map((movie) {
                      return MovieCardWidget(
                        imageUrl: movie.imagePath,
                        movieName: movie.title,
                        movieYear: movie.year,
                      );
                    }).toList(),
                  )
                : Center(
                    child: Text(
                      'No Movie or TV Show is selected',
                      style: TextStyle(fontSize: 12.sp, color: Colors.grey),
                    ),
                  ),
            SizedBox(height: 3.h),
            Column(
              children: INTEREST_CATEGORIES
                  .map((category) => InterestCategoryWidget(
                        categoryName: category,
                        allInterests: _allInterests,
                        selectedInterests: _selectedInterests,
                        isEditActive: isEditActive,
                      ))
                  .toList(),
            ),
            SizedBox(height: 3.h)
          ],
        ),
      ),
    );
  }
}

class InterestCategoryWidget extends StatelessWidget {
  final String categoryName;
  late List<MyInterest> allInterests;
  List<String> selectedInterests;
  final bool isEditActive;
  InterestCategoryWidget(
      {super.key,
      required this.categoryName,
      required this.allInterests,
      required this.selectedInterests,
      required this.isEditActive});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              categoryName,
              style: TextStyle(fontSize: 14.sp, fontWeight: FontWeight.bold),
            ),
            // SizedBox(
            //   width: 1.w,
            // ),
            if (isEditActive)
              IconButton(
                icon: const Icon(
                  Icons.edit,
                  color: thirdColor,
                ),
                onPressed: () {
                  Navigator.of(context).pushNamed(
                      SelectCategoryInterestScreen.routeName,
                      arguments: categoryName);
                  // Add the edit functionality here
                },
              ),
          ],
        ),
        SizedBox(height: .5.h),
        // Check if there are any selected interests for this category
        allInterests
                .where((interest) =>
                    interest.category == categoryName &&
                    selectedInterests.contains(interest.id))
                .toList()
                .isEmpty
            ? Text(
                'Nothing is selected in \'$categoryName\'',
                style: TextStyle(fontSize: 12.sp, color: Colors.grey),
              )
            : GridView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: allInterests
                    .where((interest) =>
                        interest.category == categoryName &&
                        selectedInterests.contains(interest.id))
                    .length,
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 3,
                  childAspectRatio: 1,
                  mainAxisSpacing: 0,
                  crossAxisSpacing: 0,
                ),
                itemBuilder: (context, index) {
                  final interest = allInterests
                      .where((interest) =>
                          interest.category == categoryName &&
                          selectedInterests.contains(interest.id))
                      .toList()[index];
                  return GestureDetector(
                    onTap: () {
                      // No editing functionality in view mode
                    },
                    child: Container(
                      decoration: BoxDecoration(
                        color: Colors.transparent,
                        borderRadius: BorderRadius.circular(1.h),
                      ),
                      child: Column(
                        children: [
                          Container(
                            height: 20.w,
                            child: Padding(
                              padding: EdgeInsets.all(1.5.w),
                              child: CachedNetworkImage(
                                imageUrl: interestR2Folder + interest.image,
                                fit: BoxFit.cover,
                                placeholder: (context, url) => LoadingWidget(),
                                errorWidget: (context, url, error) =>
                                    Icon(Icons.error, color: Colors.grey),
                              ),
                            ),
                          ),
                          SizedBox(height: 1.w),
                          Text(
                            interest.name,
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              fontWeight: FontWeight.w700,
                              fontSize: 9.sp,
                              // color: greyColor,
                              color: mainContrastColor,
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
        SizedBox(height: 5.h),
      ],
    );
  }
}
