import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:flutter_native_image/flutter_native_image.dart';
import 'package:image_cropper/image_cropper.dart'; // Add this import
import 'package:image_picker/image_picker.dart';
import 'package:provider/provider.dart';
import 'package:sizer/sizer.dart';
import 'package:zupid/common_things/analytics.dart';
import 'package:zupid/common_things/images.dart';
import 'package:zupid/common_things/my_colors.dart';
import 'package:zupid/translations/locale_keys.g.dart';
import 'package:zupid/common_things/my_fucntions.dart';
import 'package:zupid/models/myUserInfo.dart';
import 'package:zupid/widgets/loading_widget.dart';
import '../providers/database_provider.dart';

class PhotosContainer extends StatefulWidget {
  final bool showOnly2;
  const PhotosContainer({super.key, required this.showOnly2});

  @override
  State<PhotosContainer> createState() => _PhotosContainerState();
}

class _PhotosContainerState extends State<PhotosContainer> {
  /////
  bool _isAnalytics = true;
  late final MyAnalytics myAnalytics;
  //
  @override
  Widget build(BuildContext context) {
    //////////
    if (_isAnalytics) {
      myAnalytics = Provider.of<MyAnalytics>(context, listen: false);
      myAnalytics
          .setLogEvent('PhotosContainer', {"firstTime": widget.showOnly2});
      _isAnalytics = false;
    }
    final screenPadding = 2.w;
    double availableHeight = 50.h;
    final double screenWidth = 100.w - 2 * screenPadding;
    final double childWidth = screenWidth / 3;
    final double eachPhotoHeight = (availableHeight / 3);
    final double childAspectRatio = childWidth / eachPhotoHeight;
    //
    return Container(
      // color: Colors.white,
      child: GridView.builder(
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: widget.showOnly2 ? 2 : 3,
          childAspectRatio: childAspectRatio,
          crossAxisSpacing: screenPadding,
          mainAxisSpacing: screenPadding,
        ),
        physics: const NeverScrollableScrollPhysics(),
        shrinkWrap: true,
        padding: EdgeInsets.all(screenPadding),
        itemCount: widget.showOnly2 ? 2 : 6,
        itemBuilder: (context, index) {
          return EachPhotos(index: index);
        },
      ),
    );
  }
}

class EachPhotos extends StatefulWidget {
  final int index;
  const EachPhotos({
    Key? key,
    required this.index,
  }) : super(key: key);

  @override
  State<EachPhotos> createState() => _EachPhotosState();
}

class _EachPhotosState extends State<EachPhotos> {
  List<String> _photos = [];
  static const MAX_FILE_SIZE = 300; // in kb
  MyUserInfo? myUserInfo;
  bool firstTime = true;

  // Recursive compression function to keep reducing the image size until it's below 2 MB.
  // Future<File> compressFileUntilSize(File file) async {
  //   double fileSize = await file.lengthSync() / (1024 * 1024); // in MB
  //   int quality = 100;
  //   fileSize =
  //       file.lengthSync() / (1024 * 1024); // Update file size after compression
  //   // While file size is greater than 2 MB, keep compressing.
  //   while (fileSize > MAX_FILE_SIZE) {
  //     // print('compressing image');
  //     double compressionRatio = 2;
  //     quality = (100 / compressionRatio).clamp(1, 100).toInt();
  //     // print(quality);
  //     file = await FlutterNativeImage.compressImage(
  //       file.path,
  //       quality: quality,
  //     );
  //     fileSize = file.lengthSync() / (1024 * 1024);
  //   }
  //   return file;
  // }

  Future<File> compressFileUntilSize(File originalFile) async {
    const int maxSizeBytes = MAX_FILE_SIZE * 1024; // 500 KB target
    int quality = 95;

    ImageProperties props =
        await FlutterNativeImage.getImageProperties(originalFile.path);
    int width = props.width ?? 1080;
    int height = props.height ?? 1080;

    // Resize to max 1080 width (maintaining aspect ratio)
    if (width > 1080) {
      height = (height * 1080 / width).toInt();
      width = 1080;
    }

    File compressedFile = originalFile;

    // Gradually compress and resize until under size limit or quality < 20
    do {
      compressedFile = await FlutterNativeImage.compressImage(
        originalFile.path,
        quality: quality,
        targetWidth: width,
        targetHeight: height,
      );
      quality -= 5;
    } while (compressedFile.lengthSync() > maxSizeBytes && quality > 20);

    return compressedFile;
  }

  Future<void> _pickImage(int i) async {
    try {
      final imagePicker = ImagePicker();
      final pickedImage =
          await imagePicker.pickImage(source: ImageSource.gallery);
      if (pickedImage != null) {
        // Crop the image into a square
        final croppedFile = await ImageCropper().cropImage(
          sourcePath: pickedImage.path,
          aspectRatio: const CropAspectRatio(
              ratioX: 1, ratioY: 1), // Force square aspect ratio
          aspectRatioPresets: [CropAspectRatioPreset.square], // Square cropping
          compressQuality: 100,
          compressFormat: ImageCompressFormat.jpg,
          uiSettings: [
            AndroidUiSettings(
              toolbarTitle: 'Adjust Image',
              toolbarColor: mainContrastColor,
              toolbarWidgetColor: Colors.white,
              initAspectRatio: CropAspectRatioPreset.square,
              lockAspectRatio: true, // Lock aspect ratio to square
            ),
            IOSUiSettings(
              title: 'Adjust Image',
              aspectRatioLockEnabled: true, // Lock aspect ratio to square
            ),
          ],
        );

        if (croppedFile != null) {
          File imageFile = File(croppedFile.path);
          final fileSize = await imageFile.length();
          setState(() {
            _photos[i] = "isLoading";
          });

          // Compress the image if it exceeds the maximum file size
          if (fileSize > MAX_FILE_SIZE * 1024) {
            imageFile = await compressFileUntilSize(imageFile);
            print("Final image size: ${imageFile.lengthSync() / 1024} KB");
          }

          // print('Uploading image...');
          await Provider.of<DatabaseProvider>(context, listen: false)
              .uploadImage(imageFile, i);
        }
      }
    } catch (e) {
      // print('Error cropping or uploading image: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to process image. Please try again.'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final _myUserInfo =
        Provider.of<DatabaseProvider>(context, listen: true).getMyUserInfo;
    _photos = Provider.of<DatabaseProvider>(context, listen: true)
        .getUserSignedUrlMap[_myUserInfo.userId]!;
    final tempImageDirectory =
        Provider.of<DatabaseProvider>(context, listen: false)
            .tempDirectoryForImage;
    // print('each photo container');
    return GestureDetector(
      onTap: () async {
        if (_photos[widget.index] != "isLoading") {
          await _pickImage(widget.index);
        }
      },
      child: _photos[widget.index] == "isLoading"
          ? LoadingWidget()
          : _photos[widget.index] == "-1"
              ? Container(
                  decoration: BoxDecoration(
                    color: mainColor,
                    borderRadius: BorderRadius.circular(1.2.h),
                    border: Border.all(
                      color: mainContrastColor,
                      width: 1.5,
                    ),
                  ),
                  child: Icon(
                    Icons.add,
                    color: mainContrastColor,
                    size: 24.sp,
                  ),
                )
              : Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(1.2.h),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 4,
                        offset: Offset(2, 2),
                      ),
                    ],
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(.9.h),
                    child: FittedBox(
                        fit: BoxFit.cover,
                        child: Image.file(
                            File(tempImageDirectory + _photos[widget.index]))),
                  ),
                ),
    );
  }
}
