import 'package:flutter/material.dart';
import 'package:zupid/common_things/images.dart';
import 'package:sizer/sizer.dart';

class LoadingWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Center(
      child: Container(
        alignment: Alignment.center,
        // width: 50.w,
        height: 6.h,
        child: Image.asset(
          MyImages.kRose,
        ),
      ),
    );
    // const Center(
    //   child: SpinKitPumpingHeart(
    //     color: mainColor,
    //   ),
    // );
  }
}
