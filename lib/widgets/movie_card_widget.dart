import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:zupid/common_things/images.dart';
import 'package:zupid/common_things/my_colors.dart';
import 'package:zupid/common_things/my_constants.dart';
import 'package:sizer/sizer.dart';

class MovieCardWidget extends StatelessWidget {
  final String imageUrl;
  final String movieName;
  final String movieYear;

  const MovieCardWidget({
    Key? key,
    required this.imageUrl,
    required this.movieName,
    required this.movieYear,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // final _imageUrl =
    //     "https://m.media-amazon.com/images/M/MV5BZGM5NjliODgtODVlOS00OWZmLWIzYzMtMTI2OWIzMTM1ZGRhXkEyXkFqcGdeQXVyNDUzOTQ5MjY@._V1_SX300.jpg";
    return Card(
      color: Colors.transparent,
      elevation: 1,
      child: Padding(
        padding: EdgeInsets.all(2.w),
        child: Container(
          height: 40.w,
          decoration: BoxDecoration(
            color: Colors.transparent,
            borderRadius: BorderRadius.circular(15),
            boxShadow: const [
              // BoxShadow(
              //   color: mainContrastColor,
              //   blurRadius: 2,
              //   offset: Offset(1, 2),
              // ),
            ],
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              ClipRRect(
                borderRadius: BorderRadius.vertical(top: Radius.circular(15)),
                child: Image.network('$moviesR2Folder$imageUrl',
                    height: 24.w, width: double.infinity, fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                  return Image.asset(
                    MyImages.kMovieIcon, // your local placeholder
                    height: 24.w,
                    width: double.infinity,
                    fit: BoxFit.cover,
                  );
                }),
              ),
              // SizedBox(height: 1.w),
              Padding(
                padding: EdgeInsets.symmetric(vertical: 0, horizontal: 2.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    AutoSizeText(
                      movieName,
                      style: TextStyle(
                        fontWeight: FontWeight.w700,
                        fontSize: 9.sp,

                        color: mainContrastColor,
                        // color: greyColor,
                      ),
                      overflow: TextOverflow.ellipsis,
                      maxLines: 2,
                    ),
                    SizedBox(height: 1.w),
                    Text(
                      movieYear,
                      style: TextStyle(
                        fontSize: 8.sp,

                        color: mainContrastColor,
                        // color: greyColor,
                      ),
                    ),
                    SizedBox(height: 1.w),
                  ],
                ),
              ),
              // SizedBox(height: 1.w),
            ],
          ),
        ),
      ),
    );
  }
}
