import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import 'package:zupid/common_things/my_colors.dart';

class MyStackButton extends StatelessWidget {
  final Color myColor;
  final Icon myIcon;
  final double buttonRatio;

  const MyStackButton({
    required this.myColor,
    required this.myIcon,
    required this.buttonRatio,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 12.w,
      width: 12.w * buttonRatio,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(
            12), // Slightly increased border radius for a smoother look
        gradient: RadialGradient(
          center: Alignment.center,
          radius: 0.9,
          colors: [
            myColor.withOpacity(1), // Light color
            myColor.withOpacity(0.9), // Darker color
          ],
          stops: [0.0, 1.0],
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black
                .withOpacity(0.3), // Softer shadow for a modern effect
            blurRadius: 8,
            spreadRadius: 2,
            offset: Offset(0, 4),
          ),
        ],
      ),
      child: Center(child: myIcon),
    );
  }
}

class LeftSwipeButton extends StatelessWidget {
  const LeftSwipeButton({super.key});

  @override
  Widget build(BuildContext context) {
    return MyStackButton(
      // myColor: Color(0xFFE57373), // Soft Coral Red
      myColor: mainColor,
      myIcon: Icon(Icons.cancel_rounded,
          color: Colors.white.withOpacity(0.7), size: 6.w),
      buttonRatio: 1,
    );
  }
}

class RightSwipeButton extends StatelessWidget {
  const RightSwipeButton({super.key});

  @override
  Widget build(BuildContext context) {
    return MyStackButton(
      myColor: rightSwipeButtonColor, // Soft Teal
      myIcon: Icon(Icons.message, color: mainColor, size: 6.w),
      buttonRatio: 2,
    );
  }
}

class SuperSwipeButton extends StatelessWidget {
  const SuperSwipeButton({super.key});

  @override
  Widget build(BuildContext context) {
    return MyStackButton(
      myColor: Color(0xFFFFB74D), // Soft Amber
      myIcon: Icon(Icons.star, color: Colors.white, size: 6.w),
      buttonRatio: 1,
    );
  }
}
