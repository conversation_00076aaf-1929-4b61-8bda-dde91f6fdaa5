import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:sizer/sizer.dart';
import 'package:zupid/common_things/my_constants.dart';
import 'package:zupid/common_things/my_fucntions.dart';
import 'package:zupid/models/myInterest.dart';
import 'package:zupid/providers/database_provider.dart';

class InterestsContainer extends StatefulWidget {
  final List<String> user_interests_id;

  const InterestsContainer({required this.user_interests_id});
  @override
  _InterestsContainerState createState() => _InterestsContainerState();
}

class _InterestsContainerState extends State<InterestsContainer> {
  List<MyInterest> all_interests = [];

  List<Widget> _buildChips() {
    List<Widget> chips = [];

    for (String interest in widget.user_interests_id) {
      Widget chip = Chip(
          label: Text(
            findInterestNameFromId(interest, all_interests),
            style: const TextStyle(color: Colors.white),
          ),
          // avatar: Icon(
          //   interestIcons[findCategoryForInterestId(interest, all_interests)],
          //   color: Colors.white,
          // ),
          backgroundColor: Colors.black87);
      chips.add(chip);
    }
    return chips;
  }

  @override
  Widget build(BuildContext context) {
    // user_interests_id = Provider.of<DatabaseProvider>(context, listen: true)
    //     .getMyUserInfo
    //     .interest;
    all_interests =
        Provider.of<DatabaseProvider>(context, listen: true).getInterestList;
    //
    //
    return Wrap(
      spacing: 2.w,
      runSpacing: 0.w,
      children: _buildChips(),
    );
  }
}
