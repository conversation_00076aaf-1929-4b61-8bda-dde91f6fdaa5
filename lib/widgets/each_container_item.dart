import 'package:flutter/material.dart';
import 'package:zupid/common_things/my_colors.dart';
import 'package:sizer/sizer.dart';

class EachInfoContainer extends StatelessWidget {
  final String title;
  final Widget varWidget;
  const EachInfoContainer({
    required this.title,
    required this.varWidget,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 1,
      color: Colors.transparent,
      child: Container(
        width: 100.w,
        padding: EdgeInsets.symmetric(vertical: 2.h, horizontal: 4.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: TextStyle(
                fontSize: 10.sp,
                fontWeight: FontWeight.w500,
                color: mainContrastColor.withOpacity(0.8),
              ),
            ),
            SizedBox(height: .5.h),
            varWidget
          ],
        ),
      ),
    );
  }
}
