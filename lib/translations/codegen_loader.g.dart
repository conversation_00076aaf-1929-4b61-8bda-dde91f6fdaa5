// DO NOT EDIT. This is code generated via package:easy_localization/generate.dart

// ignore_for_file: prefer_single_quotes, avoid_renaming_method_parameters

import 'dart:ui';

import 'package:easy_localization/easy_localization.dart' show AssetLoader;

class CodegenLoader extends AssetLoader {
  const CodegenLoader();

  @override
  Future<Map<String, dynamic>?> load(String path, Locale locale) {
    return Future.value(mapLocales[locale.toString()]);
  }

  static const Map<String, dynamic> en = {
    "FirstTimeScreen": {
      "first_name": "First Name",
      "last_name": "Last Name",
      "sex": "Sex",
      "dob": "Date of birth",
      "city": "Enter your city",
      "next": "Next",
      "as_mentioned_in_your_identity_card":
          "* As mentioned in your identity card to verify your profile",
      "It_cannot_be_changed_later": "* It cannot be changed later",
      "Please_select_a_date": "Please select a date",
      "Please_select_a_city": "Please select a city"
    },
    "Common": {
      "please_enter_a_value": "Please enter a value",
      "please_enter_only_alphabets": "Please enter only alphabets",
      "maximum_allowed_length_is": "Maximum Allowed length is",
      "female_string": "Female",
      "male_string": "Male",
      "Load_Failed_Click_retry": "Load Failed! Click retry!",
      "Km_Away": "km away",
      "Yes": "Yes",
      "No": "No",
      "Submit": "Submit",
      "Your_account_is_banned": "Your account is banned",
      "Unmatch": "Unmatch",
      "Block_And_Report": "Block And Report",
      "Write_message": "Write message...",
      "enter_something": "Enter something",
      "Some_error_occured": "Some error occured",
      "Allow_location_access": "Allow location access",
      "close": "Close",
      "Select_Langauge": "Select Language"
    },
    "HomeScreen": {
      "Upload_atleast_2_photos": "* Upload atleast 2 photos (in frame 1 & 2)"
    },
    "SwipeScreen": {
      "All_Profiles_Viewed": "All Profiles Viewed",
      "Change_your_preferences":
          "Change your preferences settings to view more profiles",
      "No_super_likes_left": "No super likes left. Buy Premium",
      "No_likes_left": "No likes left. Buy Premium"
    },
    "SimilarScreen": {
      "Recommended_Profiles": "Recommended Profiles",
      "refreshed_every_week": "It will be refreshed every week",
      "Searching_for_more": "Searching for more similar profiles..."
    },
    "LikedYouScreen": {
      "Who_Liked_You": "Who Liked You",
      "No_profiles_to_show": "No profiles to show",
      "Swipe_More":
          "Send more messages and change your preferences settings to increase visiblility",
      "No_more_Profiles": "No more Profiles",
      "km_away": "km away"
    },
    "ChatScreen": {
      "no_matches_yet": "No Matches yet!",
      "Start_conversation": "Start conversation...",
      "Keep_Swiping": "Keep Swiping to match with someone :)"
    },
    "ProfileScreen": {
      "preferences": "Preferences",
      "Edit_information": "Edit information",
      "Settings": "Settings"
    },
    "PremiumFeatures": {
      "2_title": "Unlock 20 Daily Messages",
      "1_desc":
          "Passport to any location to match with people in different cities",
      "1_title": "Change your city",
      "2_desc": "Send up to 20 messages daily and never miss a connection",
      "4_desc":
          "Instantly view everyone who liked your profile — no more guessing, just matching!",
      "3_desc":
          "Unlock the power of Super likes to stand out and make a memorable impression on potential matches",
      "4_title": "See Who Likes You",
      "5_title": "More visibility",
      "5_desc":
          "Boost your profile’s reach and attract more attention effortlessly",
      "3_title": "5 Super likes matches per day",
      "Upgrade_to_premium":
          "Upgrade to premium to access the following features:",
      "zupid_Premium": "zupid Premium",
      "Buy_Premium": "Get Premium",
      "Buy_Premium_to_Unlock": "Get Premium to Unlock",
      "Enjoy_exclusive_access_to_following_features":
          "Enjoy exclusive access to following features:",
      "Change_your_city": "Change your city"
    },
    "PreferencesScreen": {
      "Select_preferences": "Select preferences",
      "Looking_for": "Looking for",
      "Long_Term": "Long Term",
      "Short_Term": "Short Term",
      "Dont_know_yet": "Dont know yet",
      "Dating": "Dating",
      "Female": "Female",
      "Male": "Male",
      "Choose_Age": "Choose Age",
      "Choose_Maximum_Distance": "Choose Maximum Distance",
      "km": "km",
      "Show_Verified_Only": "Show Verified Only"
    },
    "BuyPremiumScreen": {
      "Payment_Details": "Payment Details",
      "Subscription_Fee": "Subscription Fee",
      "Total_Amount": "Total Amount",
      "Upgrade_to_zupid_Premium": "Upgrade to zupid Premium:",
      "Choose_a_Subscription": "Choose a Subscription",
      "month": "month",
      "Save": "Save",
      "Buy_Now": "Buy Now",
      "Pay_Now": "Pay Now"
    },
    "EditInfoScreen": {
      "Edit_Info": "Edit Info",
      "Verification_under_process": "Verification under process",
      "Verify_your_profile": "Verify your profile",
      "Profile_Verified": "Profile Verified",
      "Enter_work": "Enter work",
      "Enter_school": "Enter school",
      "Life_Story": "Life Story",
      "Interests": "Interests",
      "Add_Interests": "Add Interests",
      "Tell_us_about_your_journey": "Tell us about your journey",
      "add_prompt": "Add Prompt",
      "Are_you_working": "Are you working?",
      "Company_or_Industry": "Company or Industry",
      "Please_enter_company": "Please enter Company",
      "Enter_college_or_school": "Enter College or School",
      "Please_enter_your_life_story": "Please enter your life story"
    },
    "SettingsScreen": {
      "Settings": "Settings",
      "About_App": "About App",
      "Terms_Conditions": "Terms & Conditions",
      "Privacy_Policy": "Privacy Policy",
      "hide_profile": "Hide Profile",
      "unhide_profile": "Unhide Profile",
      "Log_Out": "Log Out",
      "Share_with_Friends": "Share with Friends",
      "share_message_1":
          "Hey! I've been using zupid, a fantastic dating app that connects you with like-minded individuals.",
      "share_message_2":
          "It's been a great experience so far, and I thought you might enjoy it too! Check it out on the Play Store:",
      "share_message_3": "Let's find some thrilling connections together!",
      "WhatsApp_not_installed": "WhatsApp is not installed",
      "change_language": "Change Language"
    },
    "AboutAppScreen": {
      "About_zupid": "About zupid",
      "Welcome_to_zupid": "Welcome to zupid!",
      "text_1":
          "zupid is a dating app designed to help you find meaningful connections with like-minded individuals.",
      "text_2":
          "Whether you're looking for love, friendship, or companionship, zupid provides a platform for you to meet and interact with potential matches.",
      "Key_Features": "Key Features:",
      "f_1": "Create and customize your profile",
      "f_2": "Discover and connect with other users",
      "f_3": "Identity verified Profiles",
      "f_4": "Chat and exchange messages",
      "f_5": "Swipe to like or pass on profiles",
      "Premium_Features": "Premium Features:",
      "test_3":
          "We hope you enjoy your experience with zupid and make meaningful connections that last!"
    },
    "ChooseCityScreen": {"Select_your_city": "Select your city"},
    "ChooseInterestScreen": {
      "Select_interests": "Select interests",
      "allowed": "allowed"
    },
    "BlockAndReportScreen": {
      "Please_describe_your_reason_to_report_this_person":
          "Please describe your reason to report this person",
      "Thank_you_for_keeping_zupid_safe": "Thank you for keeping zupid safe",
      "Report_user": "Report user"
    },
    "LoginScreen": {
      "Created_in": "Created in",
      "Enter_your_Phone_Number": "Enter your Phone Number",
      "Enter_10_digit_Phone_Number": "Enter 10 digit Phone Number",
      "send_otp": "Send OTP"
    },
    "Match_screen": {
      "Congrats": "Congratulations!",
      "Youve_matched_with": "You've matched with",
      "Send_a_message": "Send message"
    },
    "otpscreen": {
      "Verify_Phone_Number": "Verify Phone Number",
      "Please_enter_OTP": "Please enter OTP",
      "Enter_6_digit_OTP": "Enter 6 digit OTP",
      "retry_in": "Retry In",
      "seconds": "seconds",
      "Resend_OTP": "Resend OTP",
      "Verify_OTP": "Verify OTP"
    },
    "payu_screen": {
      "tx_succes": "Transaction has been successful",
      "tx_failed": "Transaction has failed",
      "tx_cancel": "Transaction Cancelled",
      "error": "Some error occured. Please try again."
    },
    "verification_screen": {
      "Verify_Profile": "Verify Profile",
      "txt_1":
          "• Upload Adhar Card, Pan Card or Driving License for verification",
      "txt_2": "• Please upload clear photo",
      "txt_3": "• Verification is under process",
      "txt_4": "• It will take 24-48 hours to verify your identity"
    },
    "CallToast": {
      "Image_Size_should_be_less_than": "Image Size should be less than 5 Mb",
      "Image_Rejected_clearly_face":
          "Image Rejected. Please make sure face is clearly visible."
    }
  };
  static const Map<String, dynamic> hi = {
    "FirstTimeScreen": {
      "first_name": "प्रथम नाम",
      "last_name": "उपनाम",
      "sex": "लिंग",
      "dob": "जन्म की तारीख",
      "city": "अपना शहर दर्ज करें (अंग्रेजी में)",
      "next": "अगला",
      "as_mentioned_in_your_identity_card":
          "* जैसा कि आपके प्रोफ़ाइल को सत्यापित करने के लिए आपके पहचान पत्र में बताया गया है",
      "It_cannot_be_changed_later": "* इसे बाद में बदला नहीं जा सकता",
      "Please_select_a_date": "कृपया एक तिथि चुनें",
      "Please_select_a_city": "कृपया एक शहर चुनें (अंग्रेजी में)"
    },
    "Common": {
      "please_enter_a_value": "कृपया दर्ज करें",
      "please_enter_only_alphabets": "कृपया केवल अक्षर दर्ज करें",
      "maximum_allowed_length_is": "अधिकतम अनुमत लंबाई है",
      "female_string": "महिला",
      "male_string": "पुरुष",
      "Load_Failed_Click_retry":
          "लोड विफल हो गया! पुनः प्रयास करें पर क्लिक करें!",
      "Km_Away": "किमी दूर",
      "Yes": "हाँ",
      "No": "नहीं",
      "Submit": "इसे भेजें",
      "Your_account_is_banned": "आपका खाता प्रतिबंधित कर दिया गया है",
      "Unmatch": "उपयोगकर्ता को हटाएं",
      "Block_And_Report": "ब्लॉक करें और रिपोर्ट करें",
      "Write_message": "संदेश लिखना...",
      "enter_something": "कुछ दर्ज करें",
      "Some_error_occured": "कुछ त्रुटि हुई",
      "Allow_location_access": "स्थान की जानकारी की अनुमति दें",
      "close": "बंद करें",
      "Select_Langauge": "भाषा चुने"
    },
    "HomeScreen": {
      "Upload_atleast_2_photos":
          "* कम से कम 2 फ़ोटो अपलोड करें (फ़्रेम 1 और 2 में)"
    },
    "SwipeScreen": {
      "All_Profiles_Viewed": "सभी प्रोफ़ाइल देखी गईं",
      "Change_your_preferences":
          "अधिक प्रोफ़ाइल देखने के लिए अपनी प्राथमिकताएँ सेटिंग बदलें",
      "No_super_likes_left": "कोई सुपर लाइक नहीं बचा. प्रीमियम खरीदें",
      "No_likes_left": "कोई लाइक नहीं बचा. प्रीमियम खरीदें"
    },
    "SimilarScreen": {
      "Recommended_Profiles": "अनुशंसित प्रोफाइल",
      "refreshed_every_week": "इसे हर सप्ताह ताज़ा किया जाएगा",
      "Searching_for_more": "इसी तरह की और प्रोफ़ाइलें खोजी जा रही हैं..."
    },
    "LikedYouScreen": {
      "Who_Liked_You": "आपको किसने पसंद किया",
      "No_profiles_to_show": "दिखाने के लिए कोई प्रोफ़ाइल नहीं",
      "Swipe_More":
          "अधिक स्वाइप करें और दृश्यता बढ़ाने के लिए अपनी प्राथमिकताएँ सेटिंग बदलें",
      "No_more_Profiles": "कोई और प्रोफ़ाइल नहीं",
      "km_away": "किमी दूर"
    },
    "ChatScreen": {
      "no_matches_yet": "अभी तक कोई मिलान नहीं!",
      "Start_conversation": "बातचीत शुरू करें...",
      "Keep_Swiping": "किसी से मिलान करने के लिए स्वाइप करते रहें :)"
    },
    "ProfileScreen": {
      "preferences": "पसंद",
      "Edit_information": "जानकारी संपादित करें",
      "Settings": "समायोजन"
    },
    "PremiumFeatures": {
      "2_title": "असीमित स्वाइपिंग",
      "1_desc":
          "विभिन्न शहरों के लोगों से मेल खाने के लिए किसी भी स्थान का पासपोर्ट",
      "1_title": "अपना शहर बदलो",
      "2_desc":
          "बिना किसी सीमा के स्वाइप करें, अनंत मिलान संभावनाओं की खोज करें",
      "4_desc":
          "चाहे आप स्थानीय कनेक्शन पसंद करते हों या अपने क्षितिज को व्यापक बनाने का सपना देखते हों, आस-पास या देश भर के लोगों के साथ मेल-मिलाप करके आराम का आनंद लें।",
      "3_desc":
          "अलग दिखने और संभावित मैचों पर यादगार प्रभाव डालने की सुपर पसंद की शक्ति को अनलॉक करें",
      "4_title": "स्थानीय या राष्ट्रव्यापी मिलान के लिए लचीलापन",
      "5_title": "अधिक दृश्यता",
      "5_desc":
          "खोज परिणामों में अधिक प्रदर्शन प्राप्त करें और अपनी प्रोफ़ाइल को बढ़ावा दें",
      "3_title": "प्रति दिन 5 सुपर लाइक मैच",
      "Upgrade_to_premium":
          "निम्नलिखित सुविधाओं के लिए प्रीमियम में अपग्रेड करें:",
      "zupid_Premium": "हेडोस्टी प्रीमियम",
      "Buy_Premium": "प्रीमियम खरीदें",
      "Buy_Premium_to_Unlock": "अनलॉक करने के लिए प्रीमियम खरीदें",
      "Enjoy_exclusive_access_to_following_features":
          "निम्नलिखित सुविधाओं तक विशेष पहुंच का आनंद लें:",
      "Change_your_city": "अपना शहर बदलो"
    },
    "PreferencesScreen": {
      "Select_preferences": "विकल्प चुनें",
      "Looking_for": "ढूंढ रहे हैं",
      "Long_Term": "लांबी अवधि",
      "Short_Term": "लघु अवधि",
      "Dont_know_yet": "अभी तक पता नहीं",
      "Dating": "डेटिंग",
      "Female": "महिला",
      "Male": "पुरुष",
      "Choose_Age": "आयु चुनें",
      "Choose_Maximum_Distance": "अधिकतम दूरी चुनें",
      "km": "किमी",
      "Show_Verified_Only": "केवल सत्यापित दिखाएं"
    },
    "BuyPremiumScreen": {
      "Payment_Details": "भुगतान विवरण",
      "Subscription_Fee": "सदस्यता शुल्क",
      "Total_Amount": "कुल राशि",
      "Upgrade_to_zupid_Premium": "हेदोस्ती प्रीमियम में अपग्रेड करें:",
      "Choose_a_Subscription": "सदस्यता चुनें",
      "month": "महीना",
      "Save": "बचत",
      "Buy_Now": "अभी खरीदें",
      "Pay_Now": "भुगतान करें"
    },
    "EditInfoScreen": {
      "Edit_Info": "जानकारी संपादित करें",
      "Verification_under_process": "सत्यापन प्रक्रियाधीन है",
      "Verify_your_profile": "अपनी प्रोफ़ाइल सत्यापित करें",
      "Profile_Verified": "प्रोफ़ाइल सत्यापित",
      "Enter_work": "नौकरी दर्ज करें",
      "Enter_school": "स्कूल लिखें",
      "Life_Story": "जीवन की कहानी",
      "Interests": "रूचियाँ",
      "Add_Interests": "रुचियाँ चुनें",
      "Tell_us_about_your_journey": "हमें अपनी यात्रा के बारे में बताएं",
      "add_prompt": "प्रश्न चुनें",
      "Are_you_working": "आप जॉब करते हो क्या?",
      "Company_or_Industry": "कंपनी या उद्योग",
      "Please_enter_company": "कृपया अपनी कंपनी दर्ज करें",
      "Enter_college_or_school": "अपना कॉलेज या स्कूल लिखें",
      "Please_enter_your_life_story": "कृपया अपनी जीवन कहानी दर्ज करें"
    },
    "SettingsScreen": {
      "Settings": "समायोजन",
      "About_App": "ऐप के बारे में",
      "Terms_Conditions": "नियम एवं शर्तें",
      "Privacy_Policy": "गोपनीयता नीति",
      "hide_profile": "प्रोफ़ाइल छिपाएँ",
      "unhide_profile": "प्रोफ़ाइल उजागर करें",
      "Log_Out": "लॉग आउट",
      "Share_with_Friends": "दोस्तों के साथ शेयर करे",
      "share_message_1":
          "अरे! मैं zupid का उपयोग कर रहा हूं, जो एक शानदार डेटिंग ऐप है जो आपको समान विचारधारा वाले व्यक्तियों से जोड़ता है।",
      "share_message_2":
          "यह अब तक बहुत अच्छा अनुभव रहा है, और मुझे लगा कि आप भी इसका आनंद लेंगे! इसे खेल स्टोर पर देखें:",
      "share_message_3": "आइए मिलकर कुछ रोमांचक संबंध खोजें!",
      "WhatsApp_not_installed": "व्हाट्सएप इंस्टॉल नहीं है",
      "change_language": "भाषा बदलें"
    },
    "AboutAppScreen": {
      "About_zupid": "हेदोस्ती के बारे में",
      "Welcome_to_zupid": "हेदोस्ती में आपका स्वागत है!",
      "text_1":
          "हेदोस्ती एक डेटिंग ऐप है जो आपको समान विचारधारा वाले व्यक्तियों के साथ सार्थक संबंध खोजने में मदद करने के लिए डिज़ाइन किया गया है।",
      "text_2":
          "चाहे आप प्यार, दोस्ती या साथ की तलाश में हों, हेडोस्टी आपको संभावित साथियों से मिलने और बातचीत करने के लिए एक मंच प्रदान करता है।",
      "Key_Features": "प्रमुख विशेषताऐं:",
      "f_1": "अपनी प्रोफ़ाइल बनाएं और कस्टमाइज़ करें",
      "f_2": "अन्य उपयोगकर्ताओं को खोजें और उनसे जुड़ें",
      "f_3": "पहचान सत्यापित प्रोफ़ाइल",
      "f_4": "चैट करें और संदेशों का आदान-प्रदान करें",
      "f_5": "प्रोफ़ाइल को पसंद करने या आगे बढ़ाने के लिए स्वाइप करें",
      "Premium_Features": "प्रीमियम विशेषताएं:",
      "test_3":
          "हम आशा करते हैं कि आप हेयडोस्टी के साथ अपने अनुभव का आनंद लेंगे और सार्थक संबंध बनाएंगे जो लंबे समय तक टिके रहेंगे!"
    },
    "ChooseCityScreen": {"Select_your_city": "अपना शहर चुनें"},
    "ChooseInterestScreen": {
      "Select_interests": "रुचियाँ चुनें",
      "allowed": "की अनुमति है"
    },
    "BlockAndReportScreen": {
      "Please_describe_your_reason_to_report_this_person":
          "कृपया इस व्यक्ति की रिपोर्ट करने का अपना कारण बताएं",
      "Thank_you_for_keeping_zupid_safe":
          "हेडोस्टी को सुरक्षित रखने के लिए धन्यवाद",
      "Report_user": "उपयोगकर्ता को रिपोर्ट करें"
    },
    "LoginScreen": {
      "Created_in": "भारत में बनाया गया",
      "Enter_your_Phone_Number": "अपना फोन नंबर डालें",
      "Enter_10_digit_Phone_Number": "10 अंकीय फ़ोन नंबर दर्ज करें",
      "send_otp": "ओटीपी भेजें"
    },
    "Match_screen": {
      "Congrats": "बधाई हो!",
      "Youve_matched_with": "आप से मेल खा गया है",
      "Send_a_message": "एक संदेश भेजो"
    },
    "otpscreen": {
      "Verify_Phone_Number": "फ़ोन नंबर सत्यापित करें",
      "Please_enter_OTP": "कृपया ओटीपी दर्ज करें",
      "Enter_6_digit_OTP": "6 अंकों का ओटीपी दर्ज करें",
      "retry_in": "पुनः प्रयास करें",
      "seconds": "सेकंड में",
      "Resend_OTP": "ओटीपी पुनः भेजें",
      "Verify_OTP": "ओटीपी सत्यापित करें"
    },
    "payu_screen": {
      "tx_succes": "भुगतान सफल रहा है",
      "tx_failed": "भुगतान विफल हो गया है",
      "tx_cancel": "भुगतान रद्द कर दिया गया",
      "error": "कुछ त्रुटि हुई. कृपया पुन: प्रयास करें।"
    },
    "verification_screen": {
      "Verify_Profile": "प्रोफ़ाइल सत्यापित करें",
      "txt_1":
          "• सत्यापन के लिए आधार कार्ड, पैन कार्ड या ड्राइविंग लाइसेंस अपलोड करें",
      "txt_2": "• कृपया स्पष्ट फोटो अपलोड करें",
      "txt_3": "• सत्यापन प्रक्रियाधीन है",
      "txt_4": "• आपकी पहचान सत्यापित करने में 24-48 घंटे लगेंगे"
    },
    "CallToast": {
      "Image_Size_should_be_less_than": "फोटो का आकार 5 एमबी से कम होना चाहिए",
      "Image_Rejected_clearly_face":
          "फ़ोटो अस्वीकृत. कृपया सुनिश्चित करें कि चेहरा स्पष्ट रूप से दिखाई दे।"
    }
  };
  static const Map<String, Map<String, dynamic>> mapLocales = {
    "en": en,
    "hi": hi
  };
}
