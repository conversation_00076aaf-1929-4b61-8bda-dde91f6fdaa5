import 'dart:async';
import 'dart:io';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:zupid/common_things/my_colors.dart';
import 'package:zupid/common_things/my_fucntions.dart';
import 'package:zupid/providers/language_provider.dart';
import 'package:zupid/screens/about_myself_screen.dart';
import 'package:zupid/screens/account_action_screen.dart';
import 'package:zupid/screens/buy_premium_cashfree.dart';
import 'package:zupid/screens/choose_from_list_screen.dart';
import 'package:zupid/screens/choose_height_screen.dart';
import 'package:zupid/screens/choose_language.dart';
import 'package:zupid/screens/edit_movie_screen.dart';
import 'package:zupid/screens/force_prompt_screen.dart';
import 'package:zupid/screens/movie_search_screen.dart';
import 'package:zupid/screens/personality_trait_selection_screen.dart';
import 'package:zupid/screens/referral_entry_screen.dart';
import 'package:zupid/screens/referral_screen.dart';
import 'package:zupid/screens/select_category_interest_screen.dart';
import 'package:zupid/screens/startup_screen.dart';
import 'package:zupid/translations/codegen_loader.g.dart';
import 'package:path_provider/path_provider.dart';
import 'package:provider/provider.dart';
import 'package:sentry_flutter/sentry_flutter.dart';
import 'package:sizer/sizer.dart';
import 'package:zupid/providers/auth_provider.dart';
import 'package:zupid/providers/database_provider.dart';
import 'package:zupid/providers/internet_status_provider.dart';
import 'package:zupid/screens/about_app_screen.dart';
import 'package:zupid/screens/app_not_working_screen.dart';
import 'package:zupid/screens/blocked_reason_screen.dart';
import 'package:zupid/screens/buy_premium_screen.dart';
import 'package:zupid/screens/change_city_screen.dart';
import 'package:zupid/screens/chat_details_screen.dart';
import 'package:zupid/screens/choose_city_screen.dart';
import 'package:zupid/screens/choose_interest_screen.dart';
import 'package:zupid/screens/choose_prompt_screen.dart';
import 'package:zupid/screens/each_similar_screen.dart';
import 'package:zupid/screens/edit_info_screen.dart';
import 'package:zupid/screens/first_time_info_screen.dart';
import 'package:zupid/screens/home_screen.dart';
import 'package:zupid/screens/liked_you_screen.dart';
import 'package:zupid/screens/location_permission_screen.dart';
import 'package:zupid/screens/login_screen.dart';
import 'package:zupid/screens/match_screen.dart';
import 'package:zupid/screens/otp_screen.dart';
import 'package:zupid/screens/preferences_screen.dart';
import 'package:zupid/screens/premium_screen.dart';
import 'package:zupid/screens/settings_screen.dart';
import 'package:zupid/screens/user_profile_screen.dart';
import 'package:zupid/screens/verification_screen.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:zupid/screens/webpage_screen.dart';
import 'package:upgrader/upgrader.dart';
import 'common_things/analytics.dart';
import 'firebase_options.dart';

void main() async {
  logWithElapsed('start-main');
  //
  WidgetsFlutterBinding.ensureInitialized();
  // Lock to portrait mode only
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
  ]);
  //
  SystemChrome.setSystemUIOverlayStyle(
    SystemUiOverlayStyle(
      statusBarColor: mainColor,
      systemNavigationBarDividerColor: mainContrastColor.withOpacity(0.4),
      systemNavigationBarColor: mainContrastColor.withOpacity(0.4),
    ),
  );
  //
  await EasyLocalization.ensureInitialized();
  logWithElapsed('end-main');
  runApp(
    EasyLocalization(
      path: 'assets/translations',
      supportedLocales: const [Locale('en')],
      child: AppInitializer(), // NEW WIDGET BELOW
    ),
  );
  /////////////

  // SystemChrome.setSystemUIOverlayStyle(
  //   SystemUiOverlayStyle(
  //     statusBarColor: mainColor,
  //     systemNavigationBarDividerColor: mainContrastColor.withOpacity(0.4),
  //     systemNavigationBarColor: mainContrastColor.withOpacity(0.4),
  //   ),
  // );
  // await init();
  // await clearTempImageDirectoryAtStart();
  // await SentryFlutter.init(
  //   (options) {
  //     options.dsn =
  //         'https://<EMAIL>/4505200763207680';
  //     // Set tracesSampleRate to 1.0 to capture 100% of transactions for performance monitoring.
  //     // We recommend adjusting this value in production.
  //     options.tracesSampleRate = 1.0;
  //   },
  // );
  // SystemChrome.setPreferredOrientations([
  //   DeviceOrientation.portraitUp,
  // ]).then((_) {
  //   runApp(
  //     EasyLocalization(
  //       path: 'assets/translations',
  //       supportedLocales: const [
  //         // Locale('hi'),
  //         Locale('en'),
  //       ],
  //       // fallbackLocale: Locale('en'),
  //       // assetLoader: CodegenLoader(),
  //       // startLocale: Locale('en'),
  //       child: MyApp(),
  //     ),
  //   );
  // });
}

class AppInitializer extends StatefulWidget {
  @override
  State<AppInitializer> createState() => _AppInitializerState();
}

class _AppInitializerState extends State<AppInitializer> {
  bool isReady = false;

  @override
  void initState() {
    logWithElapsed('START-AppInitializer');
    super.initState();
    initStuff();
  }

  Future<void> initStuff() async {
    unawaited(clearTempImageDirectoryAtStart()); // Run in background
    unawaited(Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    ));
    unawaited(SentryFlutter.init(
      (options) {
        options.dsn =
            'https://<EMAIL>/4505200763207680';
        // Set tracesSampleRate to 1.0 to capture 100% of transactions for performance monitoring.
        // We recommend adjusting this value in production.
        options.tracesSampleRate = 1.0;
      },
    ));
    logWithElapsed('END-AppInitializer');
    setState(() => isReady = true);
  }

  @override
  Widget build(BuildContext context) {
    return isReady ? MyApp() : const MaterialApp(home: StartupScreen());
  }
}

// Function to clear the images subdirectory at app start
Future<void> clearTempImageDirectoryAtStart() async {
  final directory = await getTemporaryDirectory();
  final tempImageDir = Directory('${directory.path}/images');

  if (tempImageDir.existsSync()) {
    // Delete all files in the images directory
    tempImageDir.listSync().forEach((file) {
      // print('deleting file: ${file.path}');
      if (file is File) {
        file.deleteSync();
      }
    });
  } else {
    // If the directory does not exist, create it
    await tempImageDir.create(recursive: true);
  }
}

// Future init() async {
//   WidgetsFlutterBinding.ensureInitialized();
//   await EasyLocalization.ensureInitialized();
//   await Firebase.initializeApp(
//     options: DefaultFirebaseOptions.currentPlatform,
//   );
//   // Only call clearSavedSettings() during testing to reset internal values.
//   // await Upgrader.clearSavedSettings(); // REMOVE this for release builds
// }

class MyApp extends StatefulWidget {
  @override
  _MyAppState createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  @override
  Widget build(BuildContext context) {
    String appcastURL = 'https://www.zupid.net/upgrader.xml';
    final upgrader = Upgrader(
      storeController: UpgraderStoreController(
        onAndroid: () => UpgraderAppcastStore(appcastURL: appcastURL),
      ),
    );

    return Sizer(
      builder: (context, orientation, deviceType) => MultiProvider(
        providers: [
          ChangeNotifierProvider.value(value: MyAnalytics()),
          ChangeNotifierProvider.value(value: AuthProvider()..initAsync()),
          ChangeNotifierProvider.value(value: DatabaseProvider()),
          ChangeNotifierProvider.value(value: InternetStatusProvider()),
          // ChangeNotifierProvider.value(value: LanguageProvider()),
        ],
        child: Consumer<AuthProvider>(
          builder: (ctx, authprovider, _) => MaterialApp(
            //
            supportedLocales: context.supportedLocales,
            localizationsDelegates: context.localizationDelegates,
            locale: context.locale,
            //
            debugShowCheckedModeBanner: false,
            title: 'zupid',
            theme: ThemeData(
                scaffoldBackgroundColor: Colors.black,
                appBarTheme: AppBarTheme(
                  titleTextStyle: TextStyle(
                    color: thirdColor,
                    fontSize: 15.sp,
                    fontWeight: FontWeight.bold,
                  ),
                  backgroundColor: mainColor,
                  actionsIconTheme: IconThemeData(
                    color: mainContrastColor,
                  ),
                  iconTheme: IconThemeData(color: mainContrastColor),
                ),
                textTheme: Theme.of(context).textTheme.apply(
                      bodyColor: mainContrastColor,
                      displayColor: thirdColor,
                    ),
                iconTheme: IconThemeData(color: mainContrastColor),
                bottomNavigationBarTheme: BottomNavigationBarThemeData(
                  backgroundColor: mainContrastColor.withOpacity(0.8),
                  unselectedItemColor: mainColor.withOpacity(0.8),
                  selectedItemColor: Colors.white,
                ),
                elevatedButtonTheme: ElevatedButtonThemeData(
                  style: ButtonStyle(
                    backgroundColor: MaterialStatePropertyAll(thirdColor),
                  ),
                )),
            //// routes /////
            home: UpgradeAlert(
              upgrader: upgrader,
              child: authprovider.getIsJsonDownloaded == false
                  ? StartupScreen()
                  : authprovider.getIsAppWorking == false
                      ? AppNotWorkingScreen()
                      : authprovider.isCheckingToken
                          ? StartupScreen()
                          : authprovider.isAuthToken
                              ? authprovider.isFirstTime
                                  ?
                                  // authprovider.isLanguageSelected == false
                                  //     ? const ChooseLanguageScreen(firstTime: true,)
                                  FirstTimeInfoScreen()
                                  : authprovider.isLocation == false
                                      ? LocationPermissionScreen()
                                      : HomeScreen()
                              : authprovider.isPhoneNumberSend
                                  ? OtpScreen()
                                  : LoginScreen(),
            ),

            //////

            routes: {
              LoginScreen.routeName: (ctx) => LoginScreen(),
              StartupScreen.routeName: (ctx) => StartupScreen(),
              HomeScreen.routeName: (ctx) => HomeScreen(),
              EditInfoScreen.routeName: (ctx) => EditInfoScreen(),
              ChooseCityScreen.routeName: (ctx) => ChooseCityScreen(
                    isCityCheck: false,
                  ),
              PreferencesScreen.routeName: (ctx) => PreferencesScreen(),
              EachSimilarScreen.routeName: (ctx) => EachSimilarScreen(),
              LikedYouScreen.routeName: (ctx) => LikedYouScreen(),
              ChatDetailsScreen.routeName: (ctx) => ChatDetailsScreen(),
              FirstTimeInfoScreen.routeName: (ctx) => FirstTimeInfoScreen(),
              ChoosePromptScreen.routeName: (ctx) => ChoosePromptScreen(),
              ChooseInterestScreen.routeName: (ctx) => ChooseInterestScreen(),
              UserProfileScreen.routeName: (ctx) => UserProfileScreen(),
              VerificationScreen.routeName: (ctx) => VerificationScreen(),
              LocationPermissionScreen.routeName: (ctx) =>
                  LocationPermissionScreen(),
              BuyPremiumScreen.routeName: (ctx) => BuyPremiumScreen(),
              PremiumScreen.routeName: (ctx) => PremiumScreen(),
              ChangeCityScreen.routeName: (ctx) => ChangeCityScreen(),
              BlockedReasonScreen.routeName: (ctx) => BlockedReasonScreen(),
              MatchScreen.routeName: (ctx) => MatchScreen(),
              SettingsScreen.routeName: (ctx) => SettingsScreen(),
              AboutAppScreen.routeName: (ctx) => AboutAppScreen(),
              WebpageScreen.routeName: (ctx) => WebpageScreen(),
              ChooseLanguageScreen.routename: (ctx) => ChooseLanguageScreen(
                    firstTime: false,
                  ),
              ReferralScreen.routeName: (ctx) => ReferralScreen(),
              ReferralEntryScreen.routeName: (ctx) => ReferralEntryScreen(),
              BuyPremiumCashfreeScreen.routeName: (ctx) =>
                  BuyPremiumCashfreeScreen(),
              MovieSearchScreen.routeName: (ctx) => MovieSearchScreen(),
              SelectCategoryInterestScreen.routeName: (ctx) =>
                  SelectCategoryInterestScreen(
                    categoryName: '',
                  ),
              EditMovieScreen.routeName: (ctx) => EditMovieScreen(),
              ChooseHeightScreen.routename: (ctx) => ChooseHeightScreen(
                    isHeightCheck: false,
                  ),
              ChooseFromListScreen.routeName: (ctx) => ChooseFromListScreen(),
              PersonalityTraitSelectionScreen.routeName: (ctx) =>
                  PersonalityTraitSelectionScreen(),
              ForcePromptScreenScreen.routeName: (ctx) =>
                  ForcePromptScreenScreen(),
              AboutMyselfScreen.routeName: (ctx) => AboutMyselfScreen(),
              AccountActionsScreen.routeName: (ctx) =>
                  const AccountActionsScreen(),
            },
          ),
        ),
      ),
    );
  }
}
