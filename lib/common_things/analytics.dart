import 'package:amplitude_flutter/amplitude.dart';
import 'package:flutter/material.dart';

class MyAnalytics extends ChangeNotifier {
  late final Amplitude analytics;
  MyAnalytics() {
    // print('amplitude made');

    analytics = Amplitude.getInstance();
    analytics.setServerUrl("https://api2.amplitude.com");
    analytics.init('406fef56eac7cf6348ed9cbe23d963c7');
    analytics.trackingSessionEvents(true);
  }

  setUserId(String userId) {
    analytics.setUserId(userId, startNewSession: true);
  }

  setLogEvent(String eventName, Map<String, dynamic> eventMap) {
    analytics.logEvent(eventName, eventProperties: eventMap);
  }
}
