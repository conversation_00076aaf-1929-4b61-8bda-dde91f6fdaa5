// routePath
import 'package:flutter/material.dart';

//
// const appUrl = 'http://192.168.1.2:38080';
const appUrl = 'https://api.zupid.net';
const normalHeaders = {'Content-Type': 'application/json'};
// json file download
String jsonFileUrl =
    'https://www.zupid.net/zupid_app.json'; // Replace with the actual file URL
// interest image link public folder //
String interestR2Folder =
    'https://pub-db3cb0b2cf8042f8a54416d0ab2997c1.r2.dev/r2_zupid/interests/';
String moviesR2Folder =
    'https://pub-db3cb0b2cf8042f8a54416d0ab2997c1.r2.dev/r2_zupid/movies/';
// account
const loginRoute = 'login';
const verifyOtpRoute = 'verifyotp';
const getUserAccountRoute = 'useraccount/fetch';
// load all data at once
const loadAllRoute = 'loadall';

// userinfo
const getUserInfoRoute = 'userinfo';
const userInfoFirstUpdateRoute = 'userinfo/firstupdate';
const userInfoCityStateCountryUpdateRoute = 'userinfo/citystatecountryupdate';
const userInfoWorkRoute = 'userinfo/workupdate';
const userInfoSchoolRoute = 'userinfo/schoolupdate';
const userInfoAboutMeRoute = 'userinfo/aboutmeupdate';
const userInfoLocationRoute = 'userinfo/locationupdate';
const userInfoPromptRoute = 'userinfo/promptupdate';
const userInfoLastSeenRoute = 'userinfo/lastseenupdate';
const userInfoIsHideRoute = 'userinfo/updateishide';
const userInfoLanguageRoute = 'userinfo/updatelanguage';
const userInfoHeightRoute = 'userinfo/updateheight';
const userInfoEducationalLevelRoute = 'userinfo/updateeducationallevel';
const userInfoDrinkingRoute = 'userinfo/updatedrinking';
const userInfoSmokingRoute = 'userinfo/updatesmoking';
const userInfoDrugsRoute = 'userinfo/updatedrugs';
const userInfoMarijuanaRoute = 'userinfo/updatemarijuana';
const userInfoReligionRoute = 'userinfo/updatereligion';
const userInfoZodiacRoute = "userinfo/updatezodiac";
const userInfoSexualityRoute = "userinfo/updatesexuality";
const userInfoChildrenRoute = "userinfo/updatechildren";
const userInfoDeleteRoute = 'userinfo/updatedeleted';
const userInfoIsHideNameRoute = 'userinfo/updateishidename';

// userinfo2
const userInfo2FetchRoute = 'userinfo2/fetch';
const userInfo2FCMTokenRoute = 'userinfo2/fcmtoken';
//
const promptListRoute = 'promptlist';

// interest
const allInterestsRoute = 'interest/all';
const editInterestsRoute = 'interest/edit';
// userprefs
const getUserPrefsRoute = 'userprefs';
const updateUserPrefsRoute = 'userprefs/update';
// upload image and fetch
const uploadImageRoute = 'r2/upload';
const fetchImageRoute = 'r2/fetch';
//
const verifyImageRoute = 'verifyimage';
const getVerifyImageRoute = 'verifyimage/fetch';

// get other users profiles
const swipeScreenProfilesRoute = 'profiles/swipescreen';
const whoLikedYouScreenProfilesRoute = 'profiles/wholikedyou';
const similarProfilesRoute = 'profiles/similar';
const similarProfilesUpdateRoute = 'profiles/updatesimilar';
//
const chatDetailRoute = 'chat/chatdetails';
const createChatRoute = 'chat/create';
const fetchChatRoute = 'chat/list';
const updateUnreadChatDetailsRoute = 'chat/updateunread';
const blockChatDetailsRoute = 'chat/block';
const unmatchChatDetailsRoute = 'chat/unmatch';
const missedMessagesRoute = 'chat/missedmessages';
// matching udpate and add
const addOrUpdateMatchingRoute = 'matching/statusupdate';
// referral
const referralRoute = 'referral';
const referralBuyPremiumRoute = 'referral/buypremium';
// search city
const citySuggestionRoute = 'search/city';
//// PAYU /////
const payURoute = 'payu';
//// CASHFREE /////
const cashfreeOrderRoute = 'cashfree/order';
////////////////
/// Premium //
const changePassportLocationRoute = 'premium/passport';
////
/// new routes for zupid ///
const searchMovieRoute = 'search/movie';
const selectMovieRoute = 'movie/select';
const deleteMovieRoute = 'movie/delete';
const updatePersonalityTraitValueRoute = 'personality/updatetraitvalue';
const updatePersonalityTraitsBatchRoute = 'personality/updatetraitvaluebatch';

const realtimeRoute = 'realtime';
////////
Map<String, String> authHeaders(String token) {
  Map<String, String> map = {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer $token',
  };
  return map;
}

Map<String, IconData> interestIcons = {
  'Music': Icons.music_note,
  'Food': Icons.local_pizza_rounded,
  "Reading": Icons.library_books_rounded,
  "Sports": Icons.sports_baseball_rounded,
  "Drinks": Icons.local_bar_rounded,
  "Travelling": Icons.flight,
  "Pets": Icons.pets,
  "Values": Icons.favorite_outline_rounded,
  "TV genres": Icons.live_tv_rounded,
  "Hobbies": Icons.brush_rounded,
};

List<String> INTEREST_CATEGORIES = [
  'Sports',
  'Reading',
  'Food',
  'Drinks',
  'Music',
  'Movie & TV',
  'Hobbies',
  'Travel',
  'Pets',
];
