import '../models/myUserInfo.dart';

class PayUTestCredentials {
  //Find the test credentials from dev guide: https://devguide.payu.in/flutter-sdk-integration/getting-started-flutter-sdk/mobile-sdk-test-environment/
  // static const merchantKey = "gtKFFx"; // Add you Merchant Key
  static const merchantKey = "Y7IS1U";
  static const iosSurl = "https://www.heydosti.com/";
  static const iosFurl = "https://www.heydosti.com/";
  static const androidSurl = "https://www.payu.in/txnstatus";
  static const androidFurl = "https://www.payu.in/txnstatus";
}

//Pass these values from your app to SDK, this data is only for test purpose
class PayUParams {
  static Map createPayUPaymentParams(String _name, double _amount,
      MyUserInfo _myUserInfo, String _txnId, int phoneNumber) {
    var payUPaymentParams = {
      PayUPaymentParamKey.key: PayUTestCredentials.merchantKey,
      PayUPaymentParamKey.amount: _amount.toString(),
      PayUPaymentParamKey.productInfo: "Heydosti Premium",
      PayUPaymentParamKey.firstName: _myUserInfo.firstName,
      PayUPaymentParamKey.email: '<EMAIL>',
      PayUPaymentParamKey.phone: phoneNumber.toString(),
      PayUPaymentParamKey.ios_surl: PayUTestCredentials.iosSurl,
      PayUPaymentParamKey.ios_furl: PayUTestCredentials.iosFurl,
      PayUPaymentParamKey.android_surl: PayUTestCredentials.androidSurl,
      PayUPaymentParamKey.android_furl: PayUTestCredentials.androidFurl,
      PayUPaymentParamKey.environment: "0", //0 => Production 1 => Test
      PayUPaymentParamKey.transactionId: _txnId,
      PayUPaymentParamKey.enableNativeOTP: false,
    };
    return payUPaymentParams;
  }

  static Map createPayUConfigParams() {
    // var paymentModesOrder = [
    //   {"Wallets": "PHONEPE"},
    //   {"UPI": ""},
    //   {"Wallets": ""},
    //   // {"EMI": ""},
    //   {"NetBanking": ""},
    // ];

    // var enforcePaymentList = [
    //   {"payment_type": "UPI"},
    // ];

    // var customNotes = [
    //   {
    //     "custom_note": "Its Common custom note for testing purpose",
    //     "custom_note_category": [
    //       PayUPaymentTypeKeys.emi,
    //       PayUPaymentTypeKeys.card
    //     ]
    //   },
    //   {
    //     "custom_note": "Payment options custom note",
    //     "custom_note_category": null
    //   }
    // ];

    var payUCheckoutProConfig = {
      PayUCheckoutProConfigKeys.primaryColor: '#50C878',
      PayUCheckoutProConfigKeys.secondaryColor: "#ffffff",
      PayUCheckoutProConfigKeys.merchantName: "HeyDosti",
      PayUCheckoutProConfigKeys.merchantLogo: "mechant_logo",
      PayUCheckoutProConfigKeys.showExitConfirmationOnCheckoutScreen: true,
      PayUCheckoutProConfigKeys.showExitConfirmationOnPaymentScreen: true,
      // PayUCheckoutProConfigKeys.cartDetails: cartDetails,
      // PayUCheckoutProConfigKeys.paymentModesOrder: paymentModesOrder,
      PayUCheckoutProConfigKeys.merchantResponseTimeout: 3000,
      // PayUCheckoutProConfigKeys.customNotes: customNotes,
      PayUCheckoutProConfigKeys.autoSelectOtp: false,
      PayUCheckoutProConfigKeys.waitingTime: 3000,
      PayUCheckoutProConfigKeys.autoApprove: false,
      PayUCheckoutProConfigKeys.merchantSMSPermission: false,
      PayUCheckoutProConfigKeys.showCbToolbar: true,
      // PayUCheckoutProConfigKeys.enforcePaymentList: enforcePaymentList
    };
    return payUCheckoutProConfig;
  }
}

class PayUConstants {
  static const payUPaymentParams = "payUPaymentParams";
  static const payUCheckoutProConfig = "payUCheckoutProConfig";
  //Callback handling, Method channel name
  static const onPaymentSuccess = "onPaymentSuccess";
  static const onPaymentFailure = "onPaymentFailure";
  static const onPaymentCancel = "onPaymentCancel";
  static const onError = "onError";
  static const generateHash = "generateHash";
  static const errorMsg = "errorMsg";
  static const errorCode = "errorCode";
  static const isTxnInitiated = "isTxnInitiated";
}

class PayUHashConstantsKeys {
  static const hashName = "hashName";
  static const hashString = "hashString";
  static const hashType = "hashType";
  static const hashVersionV1 = "V1";
  static const hashVersionV2 = "V2";
  static const mcpLookup = "mcpLookup";
  static const postSalt = "postSalt";
}

//Payment request keys ------
class PayUPaymentParamKey {
  static const key = "key";
  static const amount = "amount";
  static const productInfo = "productInfo";
  static const firstName = "firstName";
  static const email = "email";
  static const phone = "phone";
  static const ios_surl = "ios_surl";
  static const ios_furl = "ios_furl";
  static const android_surl = "android_surl";
  static const android_furl = "android_furl";
  static const environment = "environment";
  static const userCredential = "userCredential";
  static const transactionId = "transactionId";
  static const additionalParam = "additionalParam";
  static const payUSIParams = "payUSIParams";
  static const splitPaymentDetails = "splitPaymentDetails";
  static const enableNativeOTP = "enableNativeOTP";
  static const userToken = "userToken"; //Offers user token -
}

class PayUSIParamsKeys {
  static const isFreeTrial = "isFreeTrial";
  static const billingAmount = "billingAmount";
  static const billingInterval = "billingInterval";
  static const paymentStartDate = "paymentStartDate";
  static const paymentEndDate = "paymentEndDate";
  static const billingCycle = "billingCycle";
  static const remarks = "remarks";
  static const billingCurrency = "billingCurrency";
  static const billingLimit = "billingLimit";
  static const billingRule = "billingRule";
}

class PayUAdditionalParamKeys {
  static const udf1 = "udf1";
  static const udf2 = "udf2";
  static const udf3 = "udf3";
  static const udf4 = "udf4";
  static const udf5 = "udf5";
  static const merchantAccessKey = "merchantAccessKey";
  static const sourceId = "sourceId"; //Sodexo source ID
}

class PayUCheckoutProConfigKeys {
  static const primaryColor = "primaryColor";
  static const secondaryColor = "secondaryColor";
  static const merchantName = "merchantName";
  static const merchantLogo = "merchantLogo";
  static const showExitConfirmationOnCheckoutScreen =
      "showExitConfirmationOnCheckoutScreen";
  static const showExitConfirmationOnPaymentScreen =
      "showExitConfirmationOnPaymentScreen";
  static const cartDetails = "cartDetails";
  static const paymentModesOrder = "paymentModesOrder";
  static const merchantResponseTimeout = "merchantResponseTimeout";
  static const customNotes = "customNotes";
  static const autoSelectOtp = "autoSelectOtp";
  static const enforcePaymentList = "enforcePaymentList";

  static const waitingTime = "waitingTime"; //-->(Android)
  static const autoApprove = "autoApprove"; //-->(Android)
  static const merchantSMSPermission = "merchantSMSPermission"; //-->(Android)
  static const showCbToolbar = "showCbToolbar"; //-->(Android)
}

class PayUPaymentTypeKeys {
  static const card = "CARD";
  static const nb = "NB";
  static const upi = "UPI";
  static const upiIntent = "UPI_INTENT";
  static const wallet = "WALLET";
  static const emi = "EMI";
  static const neftRtgs = "NEFTRTGS";
  static const l1Option = "L1_OPTION";
  static const sodexo = "SODEXO";
}


////////////////////////////////////////////////////
/////////////////

