import 'package:auto_size_text/auto_size_text.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:geolocator/geolocator.dart';
import 'package:zupid/common_things/my_colors.dart';
import 'package:zupid/models/myUserInfo.dart';
import 'package:zupid/providers/database_provider.dart';
import 'package:zupid/screens/choose_prompt_screen.dart';
import 'package:zupid/translations/locale_keys.g.dart';
import 'package:intl/intl.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:zupid/common_things/my_constants.dart';
import 'package:zupid/models/myInterest.dart';
import 'package:zupid/models/myChatDetails.dart';
import 'package:zupid/models/myUserPrefs.dart';
import 'package:provider/provider.dart';
import 'package:sizer/sizer.dart';
import 'dart:math' show Random, asin, cos, sqrt;

import '../models/myPromptList.dart';

Map<int, String> lookingForMap = {
  0: "Long Term",
  1: "Short Term",
  2: "Don't know yet",
  3: "Life Partner",
};

void callToast(String message) {
  Fluttertoast.showToast(
    msg: message,
    toastLength: Toast.LENGTH_SHORT,
    gravity: ToastGravity.CENTER,
    timeInSecForIosWeb: 1,
    backgroundColor: mainColor,
    textColor: mainContrastColor,
    fontSize: 11.sp,
  );
}

Uri routeUrl(String route) {
  String newRoute = appUrl + '/' + route;
  return Uri.parse(newRoute);
}

bool containsOnlySpaces(String str) {
  return RegExp(r"^\s*$").hasMatch(str);
}

/////
String getPromptQuestionFromId(List<MyPrompt> _promptList, String id) {
  // print('inside get prompt questionf rom id');
  // print(_promptList.length);
  MyPrompt _promptElement =
      _promptList.firstWhere((element) => (element.id == id));
  return _promptElement.question;
}

// find a category of an interest //
String findInterestNameFromId(
    String interest_id, List<MyInterest> all_interest) {
  MyInterest _interest =
      all_interest.firstWhere((element) => element.id == interest_id);
  return _interest.name;
}

// find all category names//
List<String> findAllInterestsCategory(List<MyInterest> all_interest) {
  //
  List<String> allCategories = [];
  all_interest.forEach((element) {
    if (allCategories.contains(element.category) == false) {
      allCategories.add(element.category);
    }
  });
  return allCategories;
}

// find all category name of an interest//
String findCategoryForInterestId(
    String interest_id, List<MyInterest> all_interest) {
  //
  var _interest =
      all_interest.firstWhere((element) => element.id == interest_id);
  return _interest.category;
}

int findAge(DateTime dob) {
  DateTime now = DateTime.now();
  Duration difference = now.difference(dob);
  int age = (difference.inDays ~/ 365);
  return age;
}

int calculateDistance(lat1, lon1, lat2, lon2, MyUserPrefs _myUserPrefs) {
  // print('$lat1 , $lon1, $lat2, $lon2 ');
  const p = 0.017453292519943295; // Pi/180
  const c = cos;
  var a = 0.5 -
      c((lat2 - lat1) * p) / 2 +
      c(lat1 * p) * c(lat2 * p) * (1 - c((lon2 - lon1) * p)) / 2;
  var distance = 12742 * asin(sqrt(a)); // 2*R*asin...
  int disInt = distance ~/ 1;
  // if (distance > _myUserPrefs.maxDistance) {
  //   var rng = Random();
  //   var randomNum = rng.nextInt(5) + 1;
  //   var dis = _myUserPrefs.maxDistance - randomNum;
  //   if (dis < 0) {
  //     return _myUserPrefs.maxDistance;
  //   } else {
  //     return dis;
  //   }
  // }
  return disInt;
}

String capitalizeFirstLetter(String input) {
  if (input.isEmpty) {
    return input; // Return the string as-is if it's empty.
  }

  return input[0].toUpperCase() + input.substring(1);
}

String showOnlyFirstLetter(String input) {
  if (input.isEmpty) {
    return input; // Return the string as-is if it's empty.
  }
  return input[0].toUpperCase();
}

int distanceBetweenGeoLocator(
    lat1, lon1, lat2, lon2, MyUserPrefs _myUserPrefs) {
  if (lat1 == 0 || lat2 == 0 || lon1 == 0 || lon2 == 0) {
    return -1;
  }
  return (Geolocator.distanceBetween(lat1, lon1, lat2, lon2) * .001).toInt();
}

String findLookingFor(int i) {
  if (i == 0) {
    return lookingForMap[0]!;
  } else if (i == 1) {
    return lookingForMap[1]!;
  } else if (i == 2) {
    return lookingForMap[2]!;
  } else {
    return lookingForMap[3]!;
  }
}

String findVarUserIdFromChatDetails(
    MyChatDetails _matchedChatDetail, String _myUserId) {
  String varUserId = "-1";
  if (_myUserId == _matchedChatDetail.userId1) {
    varUserId = _matchedChatDetail.userId2;
  } else {
    varUserId = _matchedChatDetail.userId1;
  }
  return varUserId;
}

//
String formatDateTime(DateTime dateTime) {
  dateTime = dateTime.add(const Duration(hours: 5, minutes: 30));
  final now = DateTime.now();
  final today = DateTime(now.year, now.month, now.day);
  final yesterday = DateTime(now.year, now.month, now.day - 1);

  if (dateTime.isAfter(today)) {
    // If it's today, show the time
    return DateFormat.jm().format(dateTime);
  } else if (dateTime.isAfter(yesterday)) {
    // If it's yesterday, show "Yesterday"
    return 'Yesterday';
  } else {
    // Otherwise, show the date
    return DateFormat('dd.MM.yyyy').format(dateTime);
  }
}

String chatDateTime(DateTime dateTime) {
  dateTime = dateTime
      .add(const Duration(hours: 5, minutes: 30)); // Adjust for timezone
  final now = DateTime.now();
  final today = DateTime(now.year, now.month, now.day);
  final yesterday = DateTime(now.year, now.month, now.day - 1);

  String formattedTime = '';
  // Always show the time
  formattedTime = (DateFormat.jm().format(dateTime));
  // print(dateTime);
  if (dateTime.year == today.year &&
      dateTime.month == today.month &&
      dateTime.day == today.day) {
    // If it's today, show "Today"
    return formattedTime;
  } else if (dateTime.year == yesterday.year &&
      dateTime.month == yesterday.month &&
      dateTime.day == yesterday.day) {
    // If it's yesterday, show "Yesterday"
    return '$formattedTime • Yesterday';
  } else {
    return '$formattedTime • ${DateFormat('dd.MM.yyyy').format(dateTime)}';
  }
}

callSnackfold(context, String message) {
  ScaffoldMessenger.of(context).showSnackBar(
    SnackBar(
      duration: Duration(seconds: 1),
      content: Text(
        message,
        style: const TextStyle(color: mainColor, fontWeight: FontWeight.bold),
      ),
      backgroundColor: mainContrastColor,
    ),
  );
}

void callSnackfoldTop(BuildContext context, String message) {
  final overlay = Overlay.of(context);
  final overlayEntry = OverlayEntry(
    builder: (context) => Positioned(
      top: MediaQuery.of(context).padding.top +
          10, // Adjust position below status bar
      left: 20,
      right: 20,
      child: Material(
        color: Colors.transparent,
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color: mainContrastColor,
            borderRadius: BorderRadius.circular(8),
            boxShadow: [
              BoxShadow(
                color: Colors.black26,
                blurRadius: 4,
                offset: Offset(0, 2),
              )
            ],
          ),
          child: Text(
            message,
            style:
                const TextStyle(color: mainColor, fontWeight: FontWeight.bold),
          ),
        ),
      ),
    ),
  );

  overlay.insert(overlayEntry);

  // Remove snackbar after 1 second
  Future.delayed(const Duration(seconds: 1), () {
    overlayEntry.remove();
  });
}

class PromptQuestionAnswerWidget extends StatelessWidget {
  const PromptQuestionAnswerWidget({
    super.key,
    required List<MyPrompt> promptList,
    // required this.widget,
    required this.promptNumber,
    required this.isEditActive,
    required this.varUserInfo,
  }) : _promptList = promptList;

  final List<MyPrompt> _promptList;
  // final PersonalityTab widget;
  final int promptNumber;
  final bool isEditActive;
  final MyUserInfo varUserInfo;

  @override
  Widget build(BuildContext context) {
    // final _myUserInfo =
    //     Provider.of<DatabaseProvider>(context, listen: true).getMyUserInfo;
    return GestureDetector(
      onTap: isEditActive
          ? () async {
              Navigator.of(context).pushNamed(ChoosePromptScreen.routeName,
                  arguments: [promptNumber]);
            }
          : null,
      child: Container(
        constraints: BoxConstraints(minHeight: 10.h),
        width: 100.w,
        child: Card(
          color: Colors.transparent,
          elevation: 1,
          child: Padding(
            padding: EdgeInsets.symmetric(vertical: 1.h, horizontal: 3.w),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                AutoSizeText(
                  getPromptQuestionFromId(_promptList,
                      varUserInfo.allInterests.prompts[promptNumber - 1].id),
                  style: TextStyle(
                    fontSize: 10.sp,
                    fontWeight: FontWeight.w500,
                    color: mainContrastColor.withOpacity(0.8),
                  ),
                ),
                SizedBox(
                  height: .1.h,
                ),
                AutoSizeText(
                  varUserInfo.allInterests.prompts[promptNumber - 1].value,
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: thirdColor.withOpacity(0.8),
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class AddPromptWidget extends StatefulWidget {
  final int promptNumber;
  const AddPromptWidget({
    required this.promptNumber,
  });

  @override
  State<AddPromptWidget> createState() => _AddPromptWidgetState();
}

class _AddPromptWidgetState extends State<AddPromptWidget> {
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
        onTap: () async {
          final existingIds =
              Provider.of<DatabaseProvider>(context, listen: false)
                  .getMyUserInfo
                  .allInterests
                  .prompts
                  .map((p) => p.id)
                  .toList();
          Navigator.of(context).pushNamed(ChoosePromptScreen.routeName,
              arguments: [widget.promptNumber, existingIds]);
        },
        child: Padding(
          padding: EdgeInsets.symmetric(vertical: 1.h, horizontal: 1.w),
          child: Container(
              height: 8.h,
              width: 100.w,
              decoration: BoxDecoration(
                color: mainContrastColor,
                border: Border.all(color: Colors.grey.shade800, width: 1),
                borderRadius: BorderRadius.circular(2.h),
                boxShadow: const [
                  BoxShadow(
                    color: Colors.black12,
                    blurRadius: 4,
                    offset: Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.add_circle_outlined, color: mainColor, size: 6.w),
                  SizedBox(width: 3.w),
                  Text(
                    "${LocaleKeys.EditInfoScreen_add_prompt.tr()} ${widget.promptNumber}",
                    style: TextStyle(
                      fontSize: 13.sp,
                      fontWeight: FontWeight.w600,
                      color: mainColor,
                    ),
                  ),
                ],
              )),
        )

        // Padding(
        //   padding: EdgeInsets.all(1.w),
        //   child: Container(
        //     height: 10.h,
        //     width: 100.w, // set the width of the children
        //     decoration: BoxDecoration(
        //       border: Border.all(color: mainColor, width: 1),
        //       borderRadius: BorderRadius.circular(2.h),
        //     ),
        //     child: Row(
        //       mainAxisAlignment: MainAxisAlignment.center,
        //       children: [
        //         const Icon(
        //           Icons.add,
        //           color: mainColor,
        //         ),
        //         SizedBox(width: 2.w),
        //         Text(
        //           "${LocaleKeys.EditInfoScreen_add_prompt.tr()} $promptNumber",
        //           style: TextStyle(
        //               fontSize: 4.5.w,
        //               fontWeight: FontWeight.w500,
        //               color: mainColor),
        //         ),
        //       ],
        //     ),
        //   ),
        // ),
        );
  }
}

String? getKeyFromValue(Map<dynamic, dynamic> map, int value) {
  return map.keys.firstWhere((key) => map[key] == value);
}


final timer = ElapsedTimer();

void logWithElapsed(String msg) {
  final elapsed = timer.elapsed();
  print('${elapsed.inMilliseconds} ms: $msg');
}


class ElapsedTimer {
  DateTime? _startTime;

  Duration elapsed() {
    final now = DateTime.now();

    if (_startTime == null) {
      // First time called, set start time and return zero duration
      _startTime = now;
      return Duration.zero;
    } else {
      // Subsequent calls return elapsed duration since _startTime
      return now.difference(_startTime!);
    }
  }
}

