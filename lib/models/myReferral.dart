class MyReferral {
  final String id;
  final String userId;
  final int totalCoins;
  final int totalReferrals;
  final String promoCode;
  final bool isReferred;
  final bool isFirst;

  MyReferral({
    required this.id,
    required this.userId,
    required this.totalCoins,
    required this.totalReferrals,
    required this.promoCode,
    required this.isReferred,
    required this.isFirst,
  });

  //
  factory MyReferral.fromMap(Map<String, dynamic> map) {
    return MyReferral(
      id: map['id'],
      userId: map['user_id'],
      totalCoins: map['total_coins'],
      totalReferrals: map['total_referrals'],
      promoCode: map['promo_code'],
      isReferred: map['is_referred'],
      isFirst: map['is_first'],
    );
  }
}
