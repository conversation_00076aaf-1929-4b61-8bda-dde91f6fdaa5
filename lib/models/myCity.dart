class MyCity {
  final String id;
  final String cityName;
  final String stateName;
  final String countryName;
  final double latitude;
  final double longitude;

  MyCity({
    required this.id,
    required this.cityName,
    required this.stateName,
    required this.countryName,
    required this.latitude,
    required this.longitude,
  });

  //
  factory MyCity.fromMap(Map<String, dynamic> map) {
    return MyCity(
      id: map['id'],
      cityName: map['city_name'] ?? '',
      stateName: map['state_name'] ?? '',
      countryName: map['country_name'] ?? '',
      latitude: double.parse(map['latitude'] ?? "0".toString()),
      longitude: double.parse(map['longitude'] ?? "0".toString()),
    );
  }
}
