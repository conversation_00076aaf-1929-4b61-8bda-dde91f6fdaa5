class MyUserInfo2 {
  final String id;
  final String userId;
  final String email;
  final bool emailVerified;
  final String passportCity;
  final String passportState;
  final String passportCountry;
  final int likesLeft;
  final int superLikesLeft;
  final int messageLeft;
  final int boostLeft;

  MyUserInfo2({
    required this.id,
    required this.userId,
    required this.email,
    required this.emailVerified,
    required this.passportCity,
    required this.passportState,
    required this.passportCountry,
    required this.likesLeft,
    required this.superLikesLeft,
    required this.messageLeft,
    required this.boostLeft,
  });
  ////
  factory MyUserInfo2.fromMap(Map<String, dynamic> map) {
    return MyUserInfo2(
      id: map['id'],
      userId: map['user_id'],
      email: map['email'],
      emailVerified: map['email_verified'],
      passportCity: map['passport_city'] ?? '',
      passportState: map['passport_state'] ?? '',
      passportCountry: map['passport_country'] ?? '',
      likesLeft: map['likes_left'],
      superLikesLeft: map['super_likes_left'],
      boostLeft: map['boost_left'],
      messageLeft: map['message_left'],
    );
  }
}
