class MyChatDetails {
  final String id;
  final String userId1;
  final String userId2;
  final int unread1;
  final int unread2;
  final bool mute1;
  final bool mute2;
  final String lastMessage;
  final DateTime lastMessageTime;
  final String firstName;
  final List<String> photosName;
  final DateTime updatedAt;
  final DateTime createdAt;
  //
  MyChatDetails({
    required this.id,
    required this.userId1,
    required this.userId2,
    required this.unread1,
    required this.unread2,
    required this.mute1,
    required this.mute2,
    required this.lastMessage,
    required this.lastMessageTime,
    required this.firstName,
    required this.photosName,
    required this.updatedAt,
    required this.createdAt,
  });
  ////
  factory MyChatDetails.fromMap(Map<String, dynamic> map) {
    return MyChatDetails(
      id: map['id'],
      userId1: map['user_id_1'],
      userId2: map['user_id_2'],
      unread1: map['unread_1'],
      unread2: map['unread_2'],
      mute1: map['mute_1'],
      mute2: map['mute_2'],
      lastMessage: map['last_message'] ?? "",
      lastMessageTime: DateTime.parse(map['last_message_time']),
      firstName: map['first_name'] ?? '',
      photosName: List<String>.from(map['photos_name']),
      updatedAt: DateTime.parse(map['updated_at']),
      createdAt: DateTime.parse(map['created_at']),
    );
  }
}
