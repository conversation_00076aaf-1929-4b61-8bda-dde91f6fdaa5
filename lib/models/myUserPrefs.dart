class MyUserPrefs {
  final String id;
  final String userId;
  // final int lookingFor;
  final int dating;
  final int minAge;
  final int maxAge;
  final int maxDistance;
  final bool verified;

  MyUserPrefs({
    required this.id,
    required this.userId,
    // required this.lookingFor,
    required this.dating,
    required this.minAge,
    required this.maxAge,
    required this.maxDistance,
    required this.verified,
  });

  //
  factory MyUserPrefs.fromMap(Map<String, dynamic> map) {
    return MyUserPrefs(
      id: map['id'],
      userId: map['user_id'],
      // lookingFor: map['looking_for'],
      dating: map['dating'],
      minAge: map['min_age'],
      maxAge: map['max_age'],
      maxDistance: map['max_distance'],
      verified: map['verified'],
    );
  }
}
