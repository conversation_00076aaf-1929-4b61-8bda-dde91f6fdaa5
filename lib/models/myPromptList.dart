class MyPrompt {
  final String id;
  final String category;
  final String question;
  final bool active;

  MyPrompt({
    required this.id,
    required this.category,
    required this.question,
    required this.active,
  });

  //
  factory MyPrompt.fromMap(Map<String, dynamic> map) {
    return MyPrompt(
      id: map['id'],
      category: map['category'],
      question: map['question'] ?? '',
      active: map['active'],
    );
  }
}
