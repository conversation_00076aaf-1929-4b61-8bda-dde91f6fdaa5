class MyChat {
  final String id;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String senderId;
  final String recieverId;
  final String message;

  MyChat({
    required this.id,
    required this.createdAt,
    required this.updatedAt,
    required this.senderId,
    required this.recieverId,
    required this.message,
  });

  //
  factory MyChat.fromMap(Map<String, dynamic> map) {
    return MyChat(
      id: map['id'],
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: DateTime.parse(map['updated_at']),
      senderId: map['sender_id'],
      recieverId: map['reciever_id'],
      message: map['message'] ?? '',
    );
  }
}
