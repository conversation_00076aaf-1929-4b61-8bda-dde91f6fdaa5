class MyPersonalityTraits {
  final String id;
  final String category;
  final String name;
  final String min;
  final String max;

  MyPersonalityTraits({
    required this.id,
    required this.category,
    required this.name,
    required this.min,
    required this.max,
  });

  //
  factory MyPersonalityTraits.fromMap(Map<String, dynamic> map) {
    return MyPersonalityTraits(
      id: map['id'],
      category: map['category'] ?? '',
      name: map['name'] ?? '',
      min: map['min'] ?? '',
      max: map['max'] ?? '',
    );
  }
}
