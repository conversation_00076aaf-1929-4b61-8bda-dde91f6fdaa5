class MyUserInfo {
  final String id;
  final bool isFirst;
  final bool isProfileComplete;
  final bool isDeleted;
  final bool isHide;
  final String userId;
  final bool isVerified;
  final String firstName;
  final String lastName;
  final DateTime dob;
  final int sex;
  final int lookingFor;
  final String school;
  final String work;
  final String aboutMe;
  final String prompt1Id;
  final String prompt1Value;
  final String prompt2Id;
  final String prompt2Value;
  final double latitude;
  final double longitude;
  final bool isMembership;
  final int planName;
  final bool isPassport;
  final String cityName;
  final List<String> photosName;
  final AllInterests allInterests;
  final String localLanguage;
  // newly added //
  final int religion;
  final int height;
  final int ethnicity;
  final int sexuality;
  final int children;
  final int zodiac;
  final int educationLevel;
  final int drinking;
  final int smoking;
  final int marijuana;
  final int drugs;
  final String hometown;
  final bool isHideName;
  final bool isIdVerified;
  MyUserInfo({
    required this.id,
    required this.isFirst,
    required this.isProfileComplete,
    required this.isDeleted,
    required this.isHide,
    required this.userId,
    required this.isVerified,
    required this.firstName,
    required this.lastName,
    required this.dob,
    required this.sex,
    required this.lookingFor,
    required this.school,
    required this.work,
    required this.aboutMe,
    required this.prompt1Id,
    required this.prompt1Value,
    required this.prompt2Id,
    required this.prompt2Value,
    required this.latitude,
    required this.longitude,
    required this.isMembership,
    required this.planName,
    required this.isPassport,
    required this.cityName,
    required this.photosName,
    required this.allInterests,
    required this.localLanguage,
    required this.religion,
    required this.height,
    required this.ethnicity,
    required this.sexuality,
    required this.children,
    required this.zodiac,
    required this.educationLevel,
    required this.drinking,
    required this.smoking,
    required this.marijuana,
    required this.drugs,
    required this.hometown,
    required this.isHideName,
    required this.isIdVerified,
  });

  // Add the copyWith method
  MyUserInfo copyWith({
    String? id,
    bool? isFirst,
    bool? isProfileComplete,
    bool? isDeleted,
    bool? isHide,
    String? userId,
    bool? isVerified,
    String? firstName,
    String? lastName,
    DateTime? dob,
    int? sex,
    int? lookingFor,
    String? school,
    String? work,
    String? aboutMe,
    String? prompt1Id,
    String? prompt1Value,
    String? prompt2Id,
    String? prompt2Value,
    double? latitude,
    double? longitude,
    bool? isMembership,
    int? planName,
    bool? isPassport,
    String? cityName,
    List<String>? photosName,
    AllInterests? allInterests,
    String? localLanguage,
    int? religion,
    int? height,
    int? ethnicity,
    int? sexuality,
    int? children,
    int? zodiac,
    int? educationLevel,
    int? drinking,
    int? smoking,
    int? marijuana,
    int? drugs,
    String? hometown,
    bool? isHideName,
    bool? isIdVerified,
  }) {
    return MyUserInfo(
      id: id ?? this.id,
      isFirst: isFirst ?? this.isFirst,
      isProfileComplete: isProfileComplete ?? this.isProfileComplete,
      isDeleted: isDeleted?? this.isDeleted,
      isHide: isHide ?? this.isHide,
      userId: userId ?? this.userId,
      isVerified: isVerified ?? this.isVerified,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      dob: dob ?? this.dob,
      sex: sex ?? this.sex,
      lookingFor: lookingFor ?? this.lookingFor,
      school: school ?? this.school,
      work: work ?? this.work,
      aboutMe: aboutMe ?? this.aboutMe,
      prompt1Id: prompt1Id ?? this.prompt1Id,
      prompt1Value: prompt1Value ?? this.prompt1Value,
      prompt2Id: prompt2Id ?? this.prompt2Id,
      prompt2Value: prompt2Value ?? this.prompt2Value,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      isMembership: isMembership ?? this.isMembership,
      planName: planName ?? this.planName,
      isPassport: isPassport ?? this.isPassport,
      cityName: cityName ?? this.cityName,
      photosName: photosName ?? this.photosName,
      allInterests: allInterests ?? this.allInterests,
      localLanguage: localLanguage ?? this.localLanguage,
      religion: religion ?? this.religion,
      height: height ?? this.height,
      ethnicity: ethnicity ?? this.ethnicity,
      sexuality: sexuality ?? this.sexuality,
      children: children ?? this.children,
      zodiac: zodiac ?? this.zodiac,
      educationLevel: educationLevel ?? this.educationLevel,
      drinking: drinking ?? this.drinking,
      smoking: smoking ?? this.smoking,
      marijuana: marijuana ?? this.marijuana,
      drugs: drugs ?? this.drugs,
      hometown: hometown ?? this.hometown,
      isHideName: isHideName ?? this.isHideName,
      isIdVerified: isIdVerified ?? this.isIdVerified,
    );
  }

  ////
  factory MyUserInfo.fromMap(Map<String, dynamic> map) {
    return MyUserInfo(
      id: map['id'],
      isHide: map['is_hide'],
      isProfileComplete: map['is_profile_complete'],
      isDeleted: map['is_deleted'],
      isFirst: map['is_first'],
      userId: map['user_id'],
      isVerified: map['is_photo_verified'],
      firstName: map['first_name'] ?? '',
      lastName: map['last_name'] ?? '',
      dob: DateTime.parse(map['dob']),
      sex: map['sex'],
      lookingFor: map['looking_for'],
      aboutMe: map['about_me'] ?? '',
      school: map['school'] ?? '',
      work: map['work'] ?? '',
      prompt1Id: map['prompt1_id'] ?? '',
      prompt1Value: map['prompt1_value'] ?? '',
      prompt2Id: map['prompt2_id'] ?? '',
      prompt2Value: map['prompt2_value'] ?? '',
      latitude: double.parse(map['latitude'].toString()),
      longitude: double.parse(map['longitude'].toString()),
      isMembership: map['is_membership'],
      planName: map['plan_name'],
      cityName: map['city_name'] ?? '',
      isPassport: map['is_passport'],
      photosName: List<String>.from(map['photos_name']),
      allInterests: AllInterests.fromMap(map['interests'] ?? {}),
      localLanguage: map['local_language'],
      // newlyd added //
      religion: map['religion'],
      height: map['height'],
      ethnicity: map['ethinicity'],
      sexuality: map['sexuality'],
      children: map['children'],
      zodiac: map['zodiac'],
      educationLevel: map['educational_level'],
      drinking: map['drinking'],
      smoking: map['smoking'],
      marijuana: map['marijuana'],
      drugs: map['drugs'],
      hometown: map['hometown'],
      isHideName: map['is_hide_name'],
      isIdVerified: map['is_id_verified'],
    );
  }
}

// Define a class for the Interest structure
class AllInterests {
  final List<Movie> movies;
  final List<String> interests;
  final List<PersonalityTraitsJson> personalityTraitsJson;
  final List<InterestPrompt> prompts;

  AllInterests({
    required this.movies,
    required this.interests,
    required this.personalityTraitsJson,
    required this.prompts,
  });

  // Add the copyWith method
  AllInterests copyWith({
    List<Movie>? movies,
    List<String>? interests,
    List<PersonalityTraitsJson>? personalityTraitsJson,
    List<InterestPrompt>? prompts,
  }) {
    return AllInterests(
      movies: movies ?? this.movies,
      interests: interests ?? this.interests,
      personalityTraitsJson:
          personalityTraitsJson ?? this.personalityTraitsJson,
      prompts: prompts ?? this.prompts,
    );
  }

  factory AllInterests.fromMap(Map<String, dynamic> map) {
    return AllInterests(
      movies: (map['movies'] as List<dynamic>?)
              ?.map((item) => Movie.fromMap(item))
              .toList() ??
          [],
      interests: List<String>.from(map['interest'] ?? []),
      personalityTraitsJson: (map['personality_traits'] as List<dynamic>?)
              ?.map((item) => PersonalityTraitsJson.fromMap(item))
              .toList() ??
          [],
      prompts: (map['prompts'] as List<dynamic>?)
              ?.map((item) => InterestPrompt.fromMap(item))
              .toList() ??
          [],
    );
  }
}

// Define a class for Movie
class Movie {
  final String imdbId;
  final String type;
  final String title;
  final String year;
  final String imagePath;

  Movie({
    required this.imdbId,
    required this.type,
    required this.title,
    required this.year,
    required this.imagePath,
  });

  factory Movie.fromMap(Map<String, dynamic> map) {
    return Movie(
      imdbId: map['ImdbId'] ?? '',
      type: map['type'] ?? '',
      title: map['Title'] ?? '',
      year: map['Year'] ?? '',
      imagePath: map['ImagePath'] ?? '',
    );
  }
}

class PersonalityTraitsJson {
  final String traitId;
  final int value;

  PersonalityTraitsJson({
    required this.traitId,
    required this.value,
  });

  factory PersonalityTraitsJson.fromMap(Map<String, dynamic> map) {
    return PersonalityTraitsJson(
      traitId: map['trait_id'] ?? '',
      value: map['value'] ?? '',
    );
  }
}

class InterestPrompt {
  final String id;
  final String value;

  InterestPrompt({
    required this.id,
    required this.value,
  });

  factory InterestPrompt.fromMap(Map<String, dynamic> map) {
    return InterestPrompt(
      id: map['id'] ?? '',
      value: map['value'] ?? '',
    );
  }
}


////////// 8 march 2025 //// addign copywith medthos 
