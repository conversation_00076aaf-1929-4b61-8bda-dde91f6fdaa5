class MyInterest {
  final String id;
  final String category;
  final String name;
  final String image;

  MyInterest({
    required this.id,
    required this.category,
    required this.name,
    required this.image,
  });

  //
  factory MyInterest.fromMap(Map<String, dynamic> map) {
    return MyInterest(
      id: map['id'],
      category: map['category'] ?? '',
      name: map['name'] ?? '',
      image: map['image'] ?? '',
    );
  }
}
