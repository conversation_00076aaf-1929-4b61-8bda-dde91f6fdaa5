import 'package:flutter/material.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';

class InternetStatusProvider with ChangeNotifier {
  //constructor
  InternetStatusProvider() {
    // print('isp made');
    voidStartStream();
  }

  //variables
  bool _currentStatus = true;
  bool get currentStatus {
    return _currentStatus;
  }

  //function to update status and notify listeeners
  voidChangeStatus(bool status) {
    // print('firsttime: $_firstTime');
    _currentStatus = status;
    notifyListeners();
  }

  Map<String, dynamic> get globalButtonStatus {
    if (_currentStatus == false) {
      return {'status': false, 'message': 'No Internet'};
    } else {
      return {'status': true, 'message': ''};
    }
  }

  //function to start stream
  voidStartStream() async {
    // print('running internet stream');
    InternetConnectionChecker().onStatusChange.listen((event) {
      if (event == InternetConnectionStatus.connected) {
        voidChangeStatus(true);
        // print('Internet Status: Connected');
      } else {
        voidChangeStatus(false);
        // print('Internet Status: Disconnected');
      }
    });
  }
}
