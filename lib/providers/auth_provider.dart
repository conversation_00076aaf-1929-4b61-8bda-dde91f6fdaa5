import 'dart:convert';
import 'dart:io';

import 'package:archive/archive_io.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:geolocator/geolocator.dart';
import 'package:http/http.dart' as http;
// import 'package:in_app_update/in_app_update.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:path_provider/path_provider.dart';
import 'package:zupid/common_things/my_constants.dart';
import 'package:zupid/common_things/my_fucntions.dart';

import '../models/myUserInfo.dart';

class AuthProvider with ChangeNotifier {
  AuthProvider() {
    // _init();
  }
  // _init() {
  //   // print('Init Auth Provider');
  //   downloadJsonFile();
  //   mySecureStorage = FlutterSecureStorage();
  //   loadTokenAndUserId();
  //   //
  // }

  Future<void> initAsync() async {
    logWithElapsed('START-initAsync');
    mySecureStorage = FlutterSecureStorage();
    downloadJsonFile();
    loadTokenAndUserId();
    logWithElapsed('END-initAsync');
  }

  // OPTIMIZATION: Check location permission in background for returning users
  Future<void> _checkLocationInBackground() async {
    print("check location permission: START (background)");
    _isLocation = await checkLocationPermission();
    print("check location permission: END (background)");
    notifyListeners(); // Update UI when location check is complete
  }

  //
  // AppUpdateInfo? _updateInfo;
  String? authToken;
  String _interestsPath = "-1";
  // variables
  String _currentLanguage = "";
  String? version;
  FlutterSecureStorage? mySecureStorage;
  bool _isCheckingToken = true;
  bool _isAuthToken = false;
  bool _isPhoneNumberSend = false;
  bool _isFirstTime = true;
  bool _isLocation = false;
  double lat = 0;
  double long = 0;

  /////
  int _phoneNumber = 1234567890;
  String _token = "-1";
  String _userId = "-1";
  MyUserInfo? _myUserInfo;
  bool _isJsonDownloaded = false;

  /// json file data ///
  ///
  bool _isAppWorking = true;
  String _notWorkingReason = "-1";
  List<dynamic> _membershipPlans = [];
  double _maximumDistance = 0;
  double _premiumMaximumDistance = 0;
  int _referralBuyPremiumCoins = 0;
  int _coinsPerReferral = 0;
  //
  bool _isLanguageSelected = false;
  //
  Map<dynamic, dynamic> _interestsLimit = {};
  Map<dynamic, dynamic> _interestsMinimum = {};
  List<dynamic> _height = [];
  Map<dynamic, dynamic> _religion = {};
  Map<dynamic, dynamic> _sexuality = {};
  Map<dynamic, dynamic> _children = {};
  Map<dynamic, dynamic> _zodiac = {};
  Map<dynamic, dynamic> _education = {};
  Map<dynamic, dynamic> _drinking = {};
  Map<dynamic, dynamic> _smoking = {};
  Map<dynamic, dynamic> _marijuana = {};
  Map<dynamic, dynamic> _drugs = {};

  //
  List<dynamic> _interests = [];
  List<dynamic> get getAllInterests {
    return _interests;
  }

  //
  int get getPhoneNumber {
    return _phoneNumber;
  }

  Map<dynamic, dynamic> get getInterestLimit {
    return _interestsLimit;
  }

  Map<dynamic, dynamic> get getInterestsMinimum {
    return _interestsMinimum;
  }

  // AppUpdateInfo get myAppUpdateInfo {
  //   return _updateInfo!;
  // }

  String get getInterestsPath {
    return _interestsPath;
  }

  String get getCurrentLanguage {
    return _currentLanguage;
  }

  MyUserInfo get getMyUserInfo {
    return _myUserInfo!;
  }

  bool get isLanguageSelected {
    return _isLanguageSelected;
  }

  bool get isLocation {
    return _isLocation;
  }

  bool get isCheckingToken {
    // print("Get-isCheckingToken");
    return _isCheckingToken;
  }

  bool get isAuthToken {
    // print("Get-isAuthToken");
    return _isAuthToken;
  }

  bool get isPhoneNumberSend {
    return _isPhoneNumberSend;
  }

  bool get isFirstTime {
    // print("Get-isFirstTime");
    return _isFirstTime;
  }

  //
  double get getLatitude {
    return lat;
  }

  double get getLongitude {
    return long;
  }

  /// json get //
  bool get getIsJsonDownloaded {
    // print("getIsJsonDownloaded");
    return _isJsonDownloaded;
  }

  bool get getIsAppWorking {
    // print("getIsAppWorking");
    return _isAppWorking;
  }

  String get getNotWorkingReason {
    return _notWorkingReason;
  }

  List<dynamic> get getMembershipPlans {
    return _membershipPlans;
  }

  List<double> get getJsonDistanceParamters {
    return [_maximumDistance, _premiumMaximumDistance];
  }

  int get getReferralBuyPremiumCoins {
    return _referralBuyPremiumCoins;
  }

  int get getCoinsPerReferral {
    return _coinsPerReferral;
  }

  String get getVersion {
    return version!;
  }

  List<dynamic> get getHeight {
    return _height;
  }

  //
  Map<dynamic, dynamic> get getReligion {
    return _religion;
  }

  Map<dynamic, dynamic> get getSexuality {
    return _sexuality;
  }

  Map<dynamic, dynamic> get getChildren {
    return _children;
  }

  Map<dynamic, dynamic> get getZodiac {
    return _zodiac;
  }

  Map<dynamic, dynamic> get getEducation {
    return _education;
  }

  Map<dynamic, dynamic> get getDrinking {
    return _drinking;
  }

  Map<dynamic, dynamic> get getSmoking {
    return _smoking;
  }

  Map<dynamic, dynamic> get getMarijuana {
    return _marijuana;
  }

  Map<dynamic, dynamic> get getDrugs {
    return _drugs;
  }

  Future<bool> shouldDownloadZip(String newDate) async {
    String? savedDate = await mySecureStorage!.read(key: 'interests_date');
    if (savedDate == null || savedDate != newDate) {
      await mySecureStorage!.write(key: 'interests_date', value: newDate);
      return true; // dates differ or not saved => download required
    }
    return false; // dates same => no download
  }

  Future<void> checkAndDownloadInterestsZip(String jsonDate) async {
    if (await shouldDownloadZip(jsonDate)) {
      await downloadAndExtractInterestsZip();
    } else {
      print('Interests zip already up-to-date, skipping download.');
    }
    _interestsPath = await mySecureStorage!.read(key: 'interests_path') ?? "-1";
  }

  Future<void> downloadAndExtractInterestsZip() async {
    final dio = Dio();

    try {
      final directory = await getApplicationDocumentsDirectory();
      await mySecureStorage!
          .write(key: 'interests_path', value: directory.path);
      final zipPath = '${directory.path}/interest.zip';
      final extractPath = '${directory.path}/interest/';

      // Step 1: Download ZIP file
      await dio.download(interestR2Folder + 'interests.zip', zipPath);

      // Step 2: Extract ZIP file
      final bytes = File(zipPath).readAsBytesSync();
      final archive = ZipDecoder().decodeBytes(bytes);

      for (final file in archive) {
        final filename = file.name;
        final filePath = '$extractPath$filename';

        if (file.isFile) {
          final outFile = File(filePath);
          await outFile.create(recursive: true);
          await outFile.writeAsBytes(file.content as List<int>);
        } else {
          final dir = Directory(filePath);
          await dir.create(recursive: true);
        }
      }
      print('All interests extracted successfully!');
    } catch (e) {
      print('Failed to download or extract interests.zip: $e');
    }
  }

  void languageSelected(String currentLanguage) {
    _isLanguageSelected = true;
    mySecureStorage!.write(key: 'language', value: currentLanguage);
    // print('selected language : $currentLanguage');
    // notifyListeners();
  }

  Future<void> downloadJsonFile() async {
    try {
      var response = await http.get(Uri.parse(jsonFileUrl));
      if (response.statusCode == 200) {
        // Parse the JSON string
        var jsonData = jsonDecode(response.body);

        // Access the JSON data and use it in your app
        _isAppWorking = jsonData['is_app_working'];
        _notWorkingReason = jsonData['not_working_reason'];
        _membershipPlans = jsonData['membership_plans'];
        _maximumDistance =
            double.parse(jsonData["maximum_distance"].toString());
        _premiumMaximumDistance =
            double.parse(jsonData["premium_maximum_distance"].toString());
        _referralBuyPremiumCoins =
            int.parse(jsonData["referral_buy_premium_coins"].toString());
        _coinsPerReferral =
            int.parse(jsonData["coins_per_referral"].toString());
        _interestsLimit = jsonData['interests_limit'];
        _interestsMinimum = jsonData['interests_minimum'];
        _height = jsonData['height'];
        _religion = jsonData['religion'];
        _sexuality = jsonData['sexuality'];
        _children = jsonData['children'];
        _zodiac = jsonData['zodiac'];
        _education = jsonData['education'];
        _drinking = jsonData['drinking'];
        _smoking = jsonData['smoking'];
        _marijuana = jsonData['marijuana'];
        _drugs = jsonData['drugs'];
        //
        // final String lastUpdatedDate = jsonData['interests_date'];
        // await checkAndDownloadInterestsZip(lastUpdatedDate);
        _isJsonDownloaded = true;
      } else {
        // print('Failed to fetch JSON file. Status code: ${response.statusCode}');
      }
      //
      PackageInfo packageInfo = await PackageInfo.fromPlatform();
      version = packageInfo.version;
      //
    } catch (e) {
      // print('Error occurred while fetching JSON file: $e');
    }
    notifyListeners();
  }

  // load token and userid
  Future<void> loadTokenAndUserId() async {
    // print('1');
    logWithElapsed('START-loadTokenAndUserId');

    // var x = await mySecureStorage!.read(key: 'token');
    // if (x != null) {
    //   _token = x;
    // }
    // // print('loadTokenAndUserId: 1');
    // var y = await mySecureStorage!.read(key: 'userId');
    // if (y != null) {
    //   _userId = y;
    // }
    // // print('loadTokenAndUserId: 2');
    // var z = await mySecureStorage!.read(key: 'language');
    // if (z != null) {
    //   _currentLanguage = z;
    // } else {
    //   _currentLanguage = "en";
    // }
    final keys = ['token', 'userId', 'language'];
    final values =
        await Future.wait(keys.map((key) => mySecureStorage!.read(key: key)));

    final token = values[0];
    final userId = values[1];
    final language = values[2];

    if (token != null) _token = token;
    if (userId != null) _userId = userId;
    _currentLanguage = language ?? "en";
    // print('loadTokenAndUserId: 3');
    //
    // print('2');
    logWithElapsed('END-loadTokenAndUserId');
    bool isOk = await fetchUserInfo();
    print('3');
    print('loadTokenAndUserId: 4');

    // print(isOk);
    if (isOk) {
      _isAuthToken = true;
      findFirstTime();
    }
    // now check first time
    //
    logWithElapsed('START-checkLocationPermission-loadTokenAndUserId');

    // OPTIMIZATION: Conditional location checking based on existing location
    bool hasExistingLocation = _myUserInfo != null &&
        _myUserInfo!.latitude != 0.0 &&
        _myUserInfo!.longitude != 0.0;

    if (hasExistingLocation) {
      // User has existing location - check in background (non-blocking)
      print("User has existing location - checking in background");
      _isCheckingToken = false;
      notifyListeners(); // Allow UI to proceed immediately

      // Check location in background for returning users
      _checkLocationInBackground();
    } else {
      // First time user or no location - block until location is set
      print("First time user or no location - blocking until location is set");
      _isLocation = await checkLocationPermission();
      _isCheckingToken = false;
      notifyListeners();
    }
    //
    logWithElapsed('END-checkLocationPermission-loadTokenAndUserId');
    // print('$x\n$y');
  }

  // store a pasteo token in secure storage
  void storeTokenAndUserId(String userId, String token) async {
    // Create storage
    // Write value
    await mySecureStorage!.write(key: 'token', value: token);
    await mySecureStorage!.write(key: 'userId', value: userId);
  }

  /// send phone request and get otp
  Future<bool> login(int phoneNumber) async {
    Map jsonBody = {"phone_number": phoneNumber};
    http.Response response = await http.post(routeUrl(loginRoute),
        headers: normalHeaders, body: jsonEncode(jsonBody));
    //
    if (response.statusCode == 200) {
      // print(response.body);
      _phoneNumber = phoneNumber;
      _isPhoneNumberSend = true;
      notifyListeners();
      return true;
    }
    return false;
  }

  Future<bool> resendlogin() async {
    Map jsonBody = {"phone_number": _phoneNumber};
    http.Response response = await http.post(routeUrl(loginRoute),
        headers: normalHeaders, body: jsonEncode(jsonBody));
    //
    if (response.statusCode == 200) {
      // print(response.body);
      notifyListeners();
      return true;
    }
    return false;
  }

  // verify by otp request
  /// send phone request and get otp
  Future<bool> verifyOtp(int otp) async {
    Map jsonBody = {"phone_number": _phoneNumber, "otp": otp};
    http.Response response = await http.post(routeUrl(verifyOtpRoute),
        headers: normalHeaders, body: jsonEncode(jsonBody));
    if (response.statusCode == 200) {
      // print(response.body);
      //
      var data = json.decode(response.body);
      _token = data['access_token'];
      _userId = data['user_id'];
      storeTokenAndUserId(_userId, _token);
      _isAuthToken = true;
      //
      await fetchUserInfo();
      findFirstTime();
      _isLocation = await checkLocationPermission();
      notifyListeners();
      //
      return true;
    }
    return false;
  }

  /// Now load userInfo Data from userinfo path
  Future<bool> fetchUserInfo() async {
    //
    // Read value
    // print('fetching userinfo for userid : $_userId);
    Map jsonBody = {"user_id": _userId, "var_user_id": _userId};
    http.Response response = await http.post(routeUrl(getUserInfoRoute),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $_token',
        },
        body: jsonEncode(jsonBody));
    if (response.statusCode == 200) {
      final jsonData = json.decode(response.body);
      // print(jsonData);
      // print(jsonData);
      // print('test:0');
      _myUserInfo = MyUserInfo.fromMap(jsonData);
      // print(_myUserinfo);
      // print('test:1');
      return true;
    }
    // print('test:2');
    return false;
  }

  // check first time using date object
  findFirstTime() {
    _isFirstTime = _myUserInfo!.isFirst;
  }

  //
  Future<bool> updateUserInfoFirstTime(Map jsonBody) async {
    // print('updating user info for first time');
    jsonBody['user_id'] = _userId;
    http.Response response = await http.post(routeUrl(userInfoFirstUpdateRoute),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $_token',
        },
        body: jsonEncode(jsonBody));
    final jsonData = json.decode(response.body);
    // print(jsonData);

    if (response.statusCode == 200) {
      _myUserInfo = MyUserInfo.fromMap(jsonData);
      // print(_myUserinfo!.dob);
      _isFirstTime = false;
      notifyListeners();
      return true;
    }
    return false;
  }

  /// check if location is enabled and fetch current location //
  Future<bool> checkLocationPermission() async {
    logWithElapsed('START-checkLocationPermission');
    print('check location permission');
    // print("1");
    logWithElapsed('START-checkLocationPermission-Geolocator');
    LocationPermission permission = await Geolocator.checkPermission();
    logWithElapsed('END-checkLocationPermission-Geolocator');

    // print("2");
    if (permission == LocationPermission.denied ||
        permission == LocationPermission.deniedForever ||
        permission == LocationPermission.unableToDetermine) {
      print('location not completed : $permission');
      // print("3");
      return false;
    } else {
      // TODO: remove comment
      // print("4");
      logWithElapsed('START-checkLocationPermission-FindLocation');
      await findLocation();
      logWithElapsed('END-checkLocationPermission-FindLocation');
      print("5");
      logWithElapsed('START-checkLocationPermission-UpdateCurrentLocation');
      await updateCurrentLocation();
      logWithElapsed('END-checkLocationPermission-UpdateCurrentLocation');
      print("6");

      print('LocationUpdate done!');
      print('is language selected: $_isLanguageSelected');
      logWithElapsed('END-checkLocationPermission');
      return true;
      //
    }
  }

  // upload current lcoation to server
  Future<void> updateCurrentLocation() async {
    print('updating user info for location');
    Map<String, dynamic> jsonBody = {
      'user_id': _userId,
      'latitude': lat,
      "longitude": long
    };

    http.Response response = await http.post(routeUrl(userInfoLocationRoute),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $_token',
        },
        body: jsonEncode(jsonBody));
    if (response.statusCode == 200) {
      final jsonData = json.decode(response.body);
      // print(jsonData);
      _myUserInfo = MyUserInfo.fromMap(jsonData);
      _isLocation = true;
      notifyListeners();
    }
    print('Done location');
  }

  // upload current lcoation to server
  Future<void> updateLocalLanguage(String language) async {
    // print('updating local language to : $language');
    Map<String, dynamic> jsonBody = {"local_language": language};

    http.Response response = await http.post(routeUrl(userInfoLanguageRoute),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $_token',
        },
        body: jsonEncode(jsonBody));
    final jsonData = json.decode(response.body);
    // print(jsonData);

    if (response.statusCode == 200) {
      _myUserInfo = MyUserInfo.fromMap(jsonData);
      _isLanguageSelected = true;
      _currentLanguage = _myUserInfo!.localLanguage;
      // print('200 : Language changed : ${_myUserInfo!.localLanguage}');
    }
  }

  //update permission to current lcoation
  Future<void> findLocation() async {
    Position position = await Geolocator.getCurrentPosition();
    lat = position.latitude;
    long = position.longitude;
    _isLocation = true;
  }

  /// logout
  Future<void> logout() async {
    await mySecureStorage!.delete(key: 'token');
    await mySecureStorage!.delete(key: 'userId');
    await mySecureStorage!.delete(key: 'language');
    _isAuthToken = false;
    _isPhoneNumberSend = false;
    _phoneNumber = 0;
    notifyListeners();
  }

  // check update of app
  // Future<void> checkForAppUpdate() async {
  //   await InAppUpdate.checkForUpdate().then((info) {
  //     print('12345');
  //     // print(info);
  //     print('123456');

  //     _updateInfo = info;
  //     print('test1 :$_updateInfo');
  //   }).catchError((e) {
  //     print('error');
  //     print(e.toString());
  //   });
  // }
}
